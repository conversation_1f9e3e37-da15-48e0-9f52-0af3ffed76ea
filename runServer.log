
> Configure project :
Fabric Loom: 1.10.5

> Task :compileJ<PERSON> UP-TO-DATE
> Task :processResources UP-TO-DATE
> Task :classes UP-TO-DATE
> Task :jar UP-TO-DATE
> Task :compileTestJava NO-SOURCE
> Task :processIncludeJars UP-TO-DATE
> Task :remapJar UP-TO-DATE
> Task :sourcesJar UP-TO-DATE
> Task :remapSourcesJar UP-TO-DATE
> Task :assemble UP-TO-DATE
> Task :processTestResources NO-SOURCE
> Task :testClasses UP-TO-DATE
> Task :test NO-SOURCE
> Task :validateAccessWidener NO-SOURCE
> Task :check UP-TO-DATE
> Task :build UP-TO-DATE
> Task :cleanRun UP-TO-DATE
> Task :generateLog4jConfig UP-TO-DATE
> Task :generateRemapClasspath UP-TO-DATE
> Task :generateDLIConfig UP-TO-DATE
> Task :configureLaunch UP-TO-DATE

> Task :runServer
[34m[05:02:17][m [32m[main/INFO][m [36m(FabricLoader/GameProvider)[m [0mLoading Minecraft 1.20.1 with Fabric Loader 0.16.13
[m[34m[05:02:17][m [32m[main/INFO][m [36m(FabricLoader)[m [0mLoading 46 mods:
	- fabric-api 0.92.5*****.1
	- fabric-api-base 0.4.32+1802ada577
	- fabric-api-lookup-api-v1 1.6.37+1802ada577
	- fabric-biome-api-v1 13.0.14+1802ada577
	- fabric-block-api-v1 1.0.12+1802ada577
	- fabric-block-view-api-v2 1.0.3+924f046a77
	- fabric-command-api-v1 1.2.35+f71b366f77
	- fabric-command-api-v2 2.2.14+1802ada577
	- fabric-commands-v0 0.2.52+df3654b377
	- fabric-containers-v0 0.1.66+df3654b377
	- fabric-content-registries-v0 4.0.13+1802ada577
	- fabric-convention-tags-v1 1.5.6+1802ada577
	- fabric-crash-report-info-v1 0.2.20+1802ada577
	- fabric-data-attachment-api-v1 1.0.2+de0fd6d177
	- fabric-data-generation-api-v1 12.3.6+1802ada577
	- fabric-dimensions-v1 2.1.55+1802ada577
	- fabric-entity-events-v1 1.6.1+1c78457f77
	- fabric-events-interaction-v0 0.6.4+13a40c6677
	- fabric-events-lifecycle-v0 0.2.64+df3654b377
	- fabric-game-rule-api-v1 1.0.41+1802ada577
	- fabric-gametest-api-v1 1.2.15+1802ada577
	- fabric-item-api-v1 2.1.29+1802ada577
	- fabric-item-group-api-v1 4.0.14+1802ada577
	- fabric-lifecycle-events-v1 2.2.23+1802ada577
	- fabric-loot-api-v2 1.2.3+1802ada577
	- fabric-loot-tables-v1 1.1.47+9e7660c677
	- fabric-message-api-v1 5.1.10+1802ada577
	- fabric-mining-level-api-v1 2.1.52+1802ada577
	- fabric-networking-api-v1 1.3.13+13a40c6677
	- fabric-networking-v0 0.3.53+df3654b377
	- fabric-object-builder-api-v1 11.1.5+e35120df77
	- fabric-particles-v1 1.1.3+1802ada577
	- fabric-recipe-api-v1 1.0.23+1802ada577
	- fabric-registry-sync-v0 2.3.5+1802ada577
	- fabric-rendering-data-attachment-v1 0.3.39+92a0d36777
	- fabric-rendering-fluids-v1 3.0.29+1802ada577
	- fabric-resource-conditions-api-v1 2.3.9+1802ada577
	- fabric-resource-loader-v0 0.11.12+fb82e9d777
	- fabric-screen-handler-api-v1 1.3.32+1802ada577
	- fabric-transfer-api-v1 3.3.6+8dd72ea377
	- fabric-transitive-access-wideners-v1 4.3.2+1802ada577
	- fabricloader 0.16.13
	- java 21
	- minecraft 1.20.1
	- mixinextras 0.4.1
	- pokecobbleclaim 1.0.0
[m[34m[05:02:17][m [32m[main/INFO][m [36m(FabricLoader/Mixin)[m [0mSpongePowered MIXIN Subsystem Version=0.8.7 Source=file:/home/<USER>/.gradle/caches/modules-2/files-2.1/net.fabricmc/sponge-mixin/0.15.4+mixin.0.8.7/6a12aacc794f1078458433116e9ed42c1cc98096/sponge-mixin-0.15.4+mixin.0.8.7.jar Service=Knot/Fabric Env=SERVER
[m[34m[05:02:18][m [32m[main/INFO][m [36m(FabricLoader/Mixin)[m [0mLoaded Fabric development mappings for mixin remapper!
[m[34m[05:02:18][m [32m[main/INFO][m [36m(FabricLoader/Mixin)[m [0mCompatibility level set to JAVA_16
[m[34m[05:02:18][m [32m[main/INFO][m [36m(FabricLoader/Mixin)[m [0mCompatibility level set to JAVA_17
[m[34m[05:02:18][m [32m[main/INFO][m [36m(FabricLoader/MixinExtras|Service)[m [0mInitializing MixinExtras via com.llamalad7.mixinextras.service.MixinExtrasServiceImpl(version=0.4.1).
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mInitializing PokeCobbleClaim mod
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mRegistering server-side network handlers
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mRegistering ConfigSynchronizer server-side packet handlers
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mRegistered CONFIG_REQUEST handler: pokecobbleclaim:config_request
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mRegistered CONFIG_UPDATE handler: pokecobbleclaim:config_update
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mReset all town and player data versions
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mFound 5 individual town files, loading...
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Mayor for player 25dc5eb7-e593-3192-87a1-eb20288493eb in town aaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player fb4c6c18-26a1-3d95-a13f-9a47871171a2 in town aaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 99e80a89-582d-3fb3-8388-49270da696a6 in town aaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 651bbe0e-980e-38d7-8fbb-83ebfec85347 in town aaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 6a350d2f-62d7-3aa3-951b-7fe208d8b2cb in town aaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 1e8814df-4b51-3d88-9dcd-89145d90282f in town aaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 83f835f2-20fa-325d-8039-400fcc96db8c in town aaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player e7c5c79a-91ee-3db1-afe8-a6ff4d4f15fa in town aaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Mayor for player 25dc5eb7-e593-3192-87a1-eb20288493eb in town aaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player e7c5c79a-91ee-3db1-afe8-a6ff4d4f15fa in town aaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 1e8814df-4b51-3d88-9dcd-89145d90282f in town aaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 6a350d2f-62d7-3aa3-951b-7fe208d8b2cb in town aaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Mayor for player 25dc5eb7-e593-3192-87a1-eb20288493eb in town aaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 99e80a89-582d-3fb3-8388-49270da696a6 in town aaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 651bbe0e-980e-38d7-8fbb-83ebfec85347 in town aaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 83f835f2-20fa-325d-8039-400fcc96db8c in town aaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player fb4c6c18-26a1-3d95-a13f-9a47871171a2 in town aaaaaaaaaa
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town d42f4ef4-ff82-4347-a77c-d322040ff0b2 - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town d42f4ef4-ff82-4347-a77c-d322040ff0b2 - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town d42f4ef4-ff82-4347-a77c-d322040ff0b2 - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town d42f4ef4-ff82-4347-a77c-d322040ff0b2 - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town d42f4ef4-ff82-4347-a77c-d322040ff0b2 - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town d42f4ef4-ff82-4347-a77c-d322040ff0b2 - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved settings to disk for town d42f4ef4-ff82-4347-a77c-d322040ff0b2
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet 6 town settings for town d42f4ef4-ff82-4347-a77c-d322040ff0b2
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mRestored town settings for town aaaaaaaaaa: {isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town d42f4ef4-ff82-4347-a77c-d322040ff0b2 - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town d42f4ef4-ff82-4347-a77c-d322040ff0b2 - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town d42f4ef4-ff82-4347-a77c-d322040ff0b2 - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town d42f4ef4-ff82-4347-a77c-d322040ff0b2 - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town d42f4ef4-ff82-4347-a77c-d322040ff0b2 - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town d42f4ef4-ff82-4347-a77c-d322040ff0b2 - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mLoaded town 'aaaaaaaaaa' with 8 players from d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player b40915cd-d3a7-3e3d-9a52-6fc5334ba2e3 in town aaaaaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 2a040a11-2e73-3076-950d-54036bd62bd7 in town aaaaaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player f72636fb-c7db-3c44-8cd4-8a6790fd9b69 in town aaaaaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 9ef817a1-6950-35d1-b5a8-3ec720c28c3d in town aaaaaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player eb08048d-a3b9-3008-984c-fcc8bb7d8893 in town aaaaaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player e56ec8e1-c640-36ab-8281-ae4d44216993 in town aaaaaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 1de0626e-6c80-37c2-856b-2a8735302b74 in town aaaaaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 5bd62aa7-8886-32aa-953e-97bdbc2a6931 in town aaaaaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 8e13e2e6-c022-3506-be47-7e24287c9023 in town aaaaaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player e4e5b30f-b972-382c-b5e5-86c2e785a8a6 in town aaaaaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 34077235-b312-3a6b-9fdd-0d5e06f91c0e in town aaaaaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 2ecec10b-3fca-3e44-9b51-4cca88e1c207 in town aaaaaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 34692304-57b2-31f8-a84c-d22c6ff753f7 in town aaaaaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player d3ee42bb-6490-3bf6-992c-01b42b8a0039 in town aaaaaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 1c207d49-b6d4-301f-bf62-f591002e4f1a in town aaaaaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player b0121b71-1aec-310d-8ee8-525ebe3fec09 in town aaaaaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 80db04bb-566c-3ad4-b0e2-8e4330dc410b in town aaaaaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player bce7419c-5709-3ee4-a73f-d19725fe2893 in town aaaaaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player b136c8aa-0654-3583-8db1-8749e12181a1 in town aaaaaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Mayor for player b40915cd-d3a7-3e3d-9a52-6fc5334ba2e3 in town aaaaaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 5bd62aa7-8886-32aa-953e-97bdbc2a6931 in town aaaaaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 1de0626e-6c80-37c2-856b-2a8735302b74 in town aaaaaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 1c207d49-b6d4-301f-bf62-f591002e4f1a in town aaaaaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 34077235-b312-3a6b-9fdd-0d5e06f91c0e in town aaaaaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player b40915cd-d3a7-3e3d-9a52-6fc5334ba2e3 in town aaaaaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 2a040a11-2e73-3076-950d-54036bd62bd7 in town aaaaaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 34692304-57b2-31f8-a84c-d22c6ff753f7 in town aaaaaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player b136c8aa-0654-3583-8db1-8749e12181a1 in town aaaaaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 9ef817a1-6950-35d1-b5a8-3ec720c28c3d in town aaaaaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 2ecec10b-3fca-3e44-9b51-4cca88e1c207 in town aaaaaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 80db04bb-566c-3ad4-b0e2-8e4330dc410b in town aaaaaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player e4e5b30f-b972-382c-b5e5-86c2e785a8a6 in town aaaaaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player e56ec8e1-c640-36ab-8281-ae4d44216993 in town aaaaaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player b0121b71-1aec-310d-8ee8-525ebe3fec09 in town aaaaaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player d3ee42bb-6490-3bf6-992c-01b42b8a0039 in town aaaaaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player eb08048d-a3b9-3008-984c-fcc8bb7d8893 in town aaaaaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 8e13e2e6-c022-3506-be47-7e24287c9023 in town aaaaaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player f72636fb-c7db-3c44-8cd4-8a6790fd9b69 in town aaaaaaaaaaaaa
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player bce7419c-5709-3ee4-a73f-d19725fe2893 in town aaaaaaaaaaaaa
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56 - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56 - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56 - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56 - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56 - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56 - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved settings to disk for town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet 6 town settings for town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mRestored town settings for town aaaaaaaaaaaaa: {isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56 - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56 - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56 - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56 - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56 - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56 - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mLoaded town 'aaaaaaaaaaaaa' with 19 players from 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 03e91d98-f031-4f21-b5f8-bb413f2cbf1b - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 03e91d98-f031-4f21-b5f8-bb413f2cbf1b - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 03e91d98-f031-4f21-b5f8-bb413f2cbf1b - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 03e91d98-f031-4f21-b5f8-bb413f2cbf1b - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 03e91d98-f031-4f21-b5f8-bb413f2cbf1b - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 03e91d98-f031-4f21-b5f8-bb413f2cbf1b - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved settings to disk for town 03e91d98-f031-4f21-b5f8-bb413f2cbf1b
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet 6 town settings for town 03e91d98-f031-4f21-b5f8-bb413f2cbf1b
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mRestored town settings for town TestSettingsPersistence: {isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 03e91d98-f031-4f21-b5f8-bb413f2cbf1b - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 03e91d98-f031-4f21-b5f8-bb413f2cbf1b - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 03e91d98-f031-4f21-b5f8-bb413f2cbf1b - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 03e91d98-f031-4f21-b5f8-bb413f2cbf1b - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 03e91d98-f031-4f21-b5f8-bb413f2cbf1b - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 03e91d98-f031-4f21-b5f8-bb413f2cbf1b - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mLoaded town 'TestSettingsPersistence' with 0 players from 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7 - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7 - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7 - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7 - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7 - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7 - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved settings to disk for town 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet 6 town settings for town 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mRestored town settings for town TestSettingsPersistence: {isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7 - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7 - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7 - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7 - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7 - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7 - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mLoaded town 'TestSettingsPersistence' with 0 players from 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Mayor for player c97b21a8-25e6-37ce-8a04-8d5bcf6f4c4d in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player fe207da4-45aa-3a2a-bf67-72a700290388 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 64579c2e-a410-37b0-995d-5aa98a8b0304 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player b2d19156-5364-397f-8e33-abd769d6319d in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 2ef1cb8d-614b-3e2e-9366-5d5cf65cf8bf in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 0aee4775-e8b4-37ed-8049-6be96ef5de8e in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player b6529468-5313-3ea4-bc60-3e6ea6cabccd in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player a240c974-e4f5-311c-82e7-f92bb39b2584 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 492dc575-b72e-3d83-b2fd-33ab63727150 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player c2506432-c159-30c0-93d8-64f2a6272277 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player a0a7ec06-d96a-3187-943b-79bf7e87b3a2 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 2737bbb4-080a-3670-89c1-780a32c86c5a in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 4941828d-27d9-3ea5-a0e2-8e881702c195 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player a205b8da-efc6-37ad-8e1d-84c0239cdd21 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player d944aee7-2684-33ce-b4ec-6dde00bdf73f in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 1ab04e7d-c966-3477-8a64-78d9a4421f72 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 2afb4ee9-7113-3f92-8544-46d36df86129 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 99963b1e-252d-3ba6-8126-9071dd762e6c in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 56a259bf-cc44-38c1-8061-dd05953c60e0 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player f2937d48-a72f-3375-bb6f-69c5f204d185 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 7d246c97-157b-31d3-a8fc-0b0f5f437b63 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 19487181-3859-391a-bf75-143cc8396d12 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 1dd34f6b-f553-3906-92e2-e13f78ae2b51 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 33e9440c-90db-3f3e-bf40-dab751114d4d in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 1acd01f1-1c8b-3758-959b-fdf4d16bd5de in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player fe412f9e-f508-30d0-b407-ff978cf8fc5c in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 77058a27-9ed9-3577-ba3a-f426b243548e in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 905847c0-6737-32d8-85e6-3f64ecd32033 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 5f452b45-a444-38e5-8393-5edeaa648813 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 043cbf82-02a9-3180-968e-af73411af080 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 3e1fbeae-13f8-3fbb-9842-63bc739da918 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 428a9eb8-eb59-3300-892c-1124ca047d38 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 3dc994a6-e4ba-3e8a-9bf9-4271ccca9810 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 9a9311e6-0e2a-3db9-849c-c74b23116910 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 8c6477df-589c-387e-8235-e07170c95726 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Mayor for player c97b21a8-25e6-37ce-8a04-8d5bcf6f4c4d in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 2737bbb4-080a-3670-89c1-780a32c86c5a in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 2ef1cb8d-614b-3e2e-9366-5d5cf65cf8bf in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 56a259bf-cc44-38c1-8061-dd05953c60e0 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 33e9440c-90db-3f3e-bf40-dab751114d4d in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 5f452b45-a444-38e5-8393-5edeaa648813 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 77058a27-9ed9-3577-ba3a-f426b243548e in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 9a9311e6-0e2a-3db9-849c-c74b23116910 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 3dc994a6-e4ba-3e8a-9bf9-4271ccca9810 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 19487181-3859-391a-bf75-143cc8396d12 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 8c6477df-589c-387e-8235-e07170c95726 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 2afb4ee9-7113-3f92-8544-46d36df86129 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 428a9eb8-eb59-3300-892c-1124ca047d38 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 0aee4775-e8b4-37ed-8049-6be96ef5de8e in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player a0a7ec06-d96a-3187-943b-79bf7e87b3a2 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player fe207da4-45aa-3a2a-bf67-72a700290388 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 492dc575-b72e-3d83-b2fd-33ab63727150 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player c2506432-c159-30c0-93d8-64f2a6272277 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player d944aee7-2684-33ce-b4ec-6dde00bdf73f in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player f2937d48-a72f-3375-bb6f-69c5f204d185 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 99963b1e-252d-3ba6-8126-9071dd762e6c in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 7d246c97-157b-31d3-a8fc-0b0f5f437b63 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 1dd34f6b-f553-3906-92e2-e13f78ae2b51 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 3e1fbeae-13f8-3fbb-9842-63bc739da918 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Mayor for player c97b21a8-25e6-37ce-8a04-8d5bcf6f4c4d in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 64579c2e-a410-37b0-995d-5aa98a8b0304 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player a240c974-e4f5-311c-82e7-f92bb39b2584 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player b6529468-5313-3ea4-bc60-3e6ea6cabccd in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player a205b8da-efc6-37ad-8e1d-84c0239cdd21 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 1acd01f1-1c8b-3758-959b-fdf4d16bd5de in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player fe412f9e-f508-30d0-b407-ff978cf8fc5c in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 1ab04e7d-c966-3477-8a64-78d9a4421f72 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 4941828d-27d9-3ea5-a0e2-8e881702c195 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 043cbf82-02a9-3180-968e-af73411af080 in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player b2d19156-5364-397f-8e33-abd769d6319d in town test
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 905847c0-6737-32d8-85e6-3f64ecd32033 in town test
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSaved settings to disk for town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mSet 16 town settings for town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mRestored town settings for town test: {image=test_test1, maxPlayers=100.0, d42f4ef4-ff82-4347-a77c-d322040ff0b2={isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, 03e91d98-f031-4f21-b5f8-bb413f2cbf1b={isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, showTownInList=true, 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7={isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, c279a2aa-6379-4f5a-9ec5-bce65fa5d52a={maxPlayers=100.0, d42f4ef4-ff82-4347-a77c-d322040ff0b2={isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, 03e91d98-f031-4f21-b5f8-bb413f2cbf1b={isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, showTownInList=true, 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7={isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, c279a2aa-6379-4f5a-9ec5-bce65fa5d52a={maxPlayers=100.0, d42f4ef4-ff82-4347-a77c-d322040ff0b2={isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, 03e91d98-f031-4f21-b5f8-bb413f2cbf1b={isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, showTownInList=true, 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7={isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, c279a2aa-6379-4f5a-9ec5-bce65fa5d52a={maxPlayers=100.0, d42f4ef4-ff82-4347-a77c-d322040ff0b2={isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, 03e91d98-f031-4f21-b5f8-bb413f2cbf1b={isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, showTownInList=true, 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7={isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, c279a2aa-6379-4f5a-9ec5-bce65fa5d52a={maxPlayers=100.0, isOpen=true, showTownInList=true, enableTownChat=true, name=test, description=yeeeee, allowPublicBuilding=false, enablePvP=false, allowMobSpawning=true}, description=yeeeee, isOpen=true, enableTownChat=true, name=test, 6a557cc3-fb5f-427a-a926-bafe2d3a0a56={isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, allowPublicBuilding=false, enablePvP=false, allowMobSpawning=true}, description=yeeeee, isOpen=true, enableTownChat=true, name=test, 6a557cc3-fb5f-427a-a926-bafe2d3a0a56={isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, allowPublicBuilding=false, enablePvP=false, allowMobSpawning=true}, description=yeeeee, isOpen=true, enableTownChat=true, name=test, 6a557cc3-fb5f-427a-a926-bafe2d3a0a56={isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, allowPublicBuilding=false, enablePvP=false, allowMobSpawning=true}, description=yeeee, imageSettings={offsetX=0.0, offsetY=0.0, scale=1.0}, isOpen=true, enableTownChat=true, name=test, 6a557cc3-fb5f-427a-a926-bafe2d3a0a56={isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, allowPublicBuilding=false, enablePvP=false, allowMobSpawning=true}
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [33m[main/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mLoaded town 'test' with 35 players from c279a2aa-6379-4f5a-9ec5-bce65fa5d52a.json
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mRestored 62 player-town relationships
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mLoaded 5 towns from individual files
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mTown data loading completed. Final town count in TownManager: 5
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mPokeCobbleClaim mod initialized successfully
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mInitializing PokeCobbleClaim server components
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mLoaded backup configuration
[m[34m[05:02:26][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mPokeCobbleClaim server components initialized successfully
[m[34m[05:02:26][m [32m[main/INFO][m [36m(Minecraft)[m [0mEnvironment: authHost='https://authserver.mojang.com', accountsHost='https://api.mojang.com', sessionHost='https://sessionserver.mojang.com', servicesHost='https://api.minecraftservices.com', name='PROD'
[m[34m[05:02:28][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mRegistering server-side town commands
[m[34m[05:02:28][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mRegistering town admin commands
[m[34m[05:02:28][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mTown admin commands registered successfully
[m[34m[05:02:28][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mRegistered backup command
[m[34m[05:02:28][m [32m[main/INFO][m [36m(pokecobbleclaim)[m [0mServer-side town commands registered successfully
[m[34m[05:02:28][m [32m[main/INFO][m [36m(Minecraft)[m [0mLoaded 7 recipes
[m[34m[05:02:28][m [32m[main/INFO][m [36m(Minecraft)[m [0mLoaded 1271 advancements
[m[34m[05:02:29][m [32m[main/INFO][m [36m(BiomeModificationImpl)[m [0mApplied 0 biome modifications to 0 of 64 new biomes in 4.653 ms
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSetting server instance in TownManager and PlayerDataManager
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mServer instance set in TownManager
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mPlayer data storage system initialized
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mPlayerDataManager initialized
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0m[System] Error logging system initialized
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0m[Server] Server started
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mReal-time status manager initialized
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading town data during server startup
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mFound 5 individual town files, loading...
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Mayor for player 25dc5eb7-e593-3192-87a1-eb20288493eb in town aaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player fb4c6c18-26a1-3d95-a13f-9a47871171a2 in town aaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 99e80a89-582d-3fb3-8388-49270da696a6 in town aaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 651bbe0e-980e-38d7-8fbb-83ebfec85347 in town aaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 6a350d2f-62d7-3aa3-951b-7fe208d8b2cb in town aaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 1e8814df-4b51-3d88-9dcd-89145d90282f in town aaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 83f835f2-20fa-325d-8039-400fcc96db8c in town aaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player e7c5c79a-91ee-3db1-afe8-a6ff4d4f15fa in town aaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Mayor for player 25dc5eb7-e593-3192-87a1-eb20288493eb in town aaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player e7c5c79a-91ee-3db1-afe8-a6ff4d4f15fa in town aaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 1e8814df-4b51-3d88-9dcd-89145d90282f in town aaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 6a350d2f-62d7-3aa3-951b-7fe208d8b2cb in town aaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Mayor for player 25dc5eb7-e593-3192-87a1-eb20288493eb in town aaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 99e80a89-582d-3fb3-8388-49270da696a6 in town aaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 651bbe0e-980e-38d7-8fbb-83ebfec85347 in town aaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 83f835f2-20fa-325d-8039-400fcc96db8c in town aaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player fb4c6c18-26a1-3d95-a13f-9a47871171a2 in town aaaaaaaaaa
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town d42f4ef4-ff82-4347-a77c-d322040ff0b2 - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town d42f4ef4-ff82-4347-a77c-d322040ff0b2 - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town d42f4ef4-ff82-4347-a77c-d322040ff0b2 - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town d42f4ef4-ff82-4347-a77c-d322040ff0b2 - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town d42f4ef4-ff82-4347-a77c-d322040ff0b2 - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town d42f4ef4-ff82-4347-a77c-d322040ff0b2 - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved settings to disk for town d42f4ef4-ff82-4347-a77c-d322040ff0b2
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet 6 town settings for town d42f4ef4-ff82-4347-a77c-d322040ff0b2
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mRestored town settings for town aaaaaaaaaa: {isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town d42f4ef4-ff82-4347-a77c-d322040ff0b2 - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town d42f4ef4-ff82-4347-a77c-d322040ff0b2 - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town d42f4ef4-ff82-4347-a77c-d322040ff0b2 - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town d42f4ef4-ff82-4347-a77c-d322040ff0b2 - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town d42f4ef4-ff82-4347-a77c-d322040ff0b2 - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town d42f4ef4-ff82-4347-a77c-d322040ff0b2 - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mLoaded town 'aaaaaaaaaa' with 8 players from d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player b40915cd-d3a7-3e3d-9a52-6fc5334ba2e3 in town aaaaaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 2a040a11-2e73-3076-950d-54036bd62bd7 in town aaaaaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player f72636fb-c7db-3c44-8cd4-8a6790fd9b69 in town aaaaaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 9ef817a1-6950-35d1-b5a8-3ec720c28c3d in town aaaaaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player eb08048d-a3b9-3008-984c-fcc8bb7d8893 in town aaaaaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player e56ec8e1-c640-36ab-8281-ae4d44216993 in town aaaaaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 1de0626e-6c80-37c2-856b-2a8735302b74 in town aaaaaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 5bd62aa7-8886-32aa-953e-97bdbc2a6931 in town aaaaaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 8e13e2e6-c022-3506-be47-7e24287c9023 in town aaaaaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player e4e5b30f-b972-382c-b5e5-86c2e785a8a6 in town aaaaaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 34077235-b312-3a6b-9fdd-0d5e06f91c0e in town aaaaaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 2ecec10b-3fca-3e44-9b51-4cca88e1c207 in town aaaaaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 34692304-57b2-31f8-a84c-d22c6ff753f7 in town aaaaaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player d3ee42bb-6490-3bf6-992c-01b42b8a0039 in town aaaaaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 1c207d49-b6d4-301f-bf62-f591002e4f1a in town aaaaaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player b0121b71-1aec-310d-8ee8-525ebe3fec09 in town aaaaaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 80db04bb-566c-3ad4-b0e2-8e4330dc410b in town aaaaaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player bce7419c-5709-3ee4-a73f-d19725fe2893 in town aaaaaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player b136c8aa-0654-3583-8db1-8749e12181a1 in town aaaaaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Mayor for player b40915cd-d3a7-3e3d-9a52-6fc5334ba2e3 in town aaaaaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 5bd62aa7-8886-32aa-953e-97bdbc2a6931 in town aaaaaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 1de0626e-6c80-37c2-856b-2a8735302b74 in town aaaaaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 1c207d49-b6d4-301f-bf62-f591002e4f1a in town aaaaaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 34077235-b312-3a6b-9fdd-0d5e06f91c0e in town aaaaaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player b40915cd-d3a7-3e3d-9a52-6fc5334ba2e3 in town aaaaaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 2a040a11-2e73-3076-950d-54036bd62bd7 in town aaaaaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 34692304-57b2-31f8-a84c-d22c6ff753f7 in town aaaaaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player b136c8aa-0654-3583-8db1-8749e12181a1 in town aaaaaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 9ef817a1-6950-35d1-b5a8-3ec720c28c3d in town aaaaaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 2ecec10b-3fca-3e44-9b51-4cca88e1c207 in town aaaaaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 80db04bb-566c-3ad4-b0e2-8e4330dc410b in town aaaaaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player e4e5b30f-b972-382c-b5e5-86c2e785a8a6 in town aaaaaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player e56ec8e1-c640-36ab-8281-ae4d44216993 in town aaaaaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player b0121b71-1aec-310d-8ee8-525ebe3fec09 in town aaaaaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player d3ee42bb-6490-3bf6-992c-01b42b8a0039 in town aaaaaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player eb08048d-a3b9-3008-984c-fcc8bb7d8893 in town aaaaaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 8e13e2e6-c022-3506-be47-7e24287c9023 in town aaaaaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player f72636fb-c7db-3c44-8cd4-8a6790fd9b69 in town aaaaaaaaaaaaa
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player bce7419c-5709-3ee4-a73f-d19725fe2893 in town aaaaaaaaaaaaa
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56 - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56 - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56 - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56 - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56 - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56 - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved settings to disk for town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet 6 town settings for town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mRestored town settings for town aaaaaaaaaaaaa: {isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56 - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56 - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56 - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56 - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56 - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56 - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mLoaded town 'aaaaaaaaaaaaa' with 19 players from 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 03e91d98-f031-4f21-b5f8-bb413f2cbf1b - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 03e91d98-f031-4f21-b5f8-bb413f2cbf1b - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 03e91d98-f031-4f21-b5f8-bb413f2cbf1b - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 03e91d98-f031-4f21-b5f8-bb413f2cbf1b - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 03e91d98-f031-4f21-b5f8-bb413f2cbf1b - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 03e91d98-f031-4f21-b5f8-bb413f2cbf1b - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved settings to disk for town 03e91d98-f031-4f21-b5f8-bb413f2cbf1b
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet 6 town settings for town 03e91d98-f031-4f21-b5f8-bb413f2cbf1b
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mRestored town settings for town TestSettingsPersistence: {isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 03e91d98-f031-4f21-b5f8-bb413f2cbf1b - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 03e91d98-f031-4f21-b5f8-bb413f2cbf1b - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 03e91d98-f031-4f21-b5f8-bb413f2cbf1b - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 03e91d98-f031-4f21-b5f8-bb413f2cbf1b - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 03e91d98-f031-4f21-b5f8-bb413f2cbf1b - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 03e91d98-f031-4f21-b5f8-bb413f2cbf1b - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mLoaded town 'TestSettingsPersistence' with 0 players from 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7 - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7 - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7 - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7 - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7 - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7 - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved settings to disk for town 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet 6 town settings for town 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mRestored town settings for town TestSettingsPersistence: {isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7 - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7 - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7 - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7 - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7 - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7 - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mLoaded town 'TestSettingsPersistence' with 0 players from 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Mayor for player c97b21a8-25e6-37ce-8a04-8d5bcf6f4c4d in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player fe207da4-45aa-3a2a-bf67-72a700290388 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 64579c2e-a410-37b0-995d-5aa98a8b0304 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player b2d19156-5364-397f-8e33-abd769d6319d in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 2ef1cb8d-614b-3e2e-9366-5d5cf65cf8bf in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 0aee4775-e8b4-37ed-8049-6be96ef5de8e in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player b6529468-5313-3ea4-bc60-3e6ea6cabccd in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player a240c974-e4f5-311c-82e7-f92bb39b2584 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 492dc575-b72e-3d83-b2fd-33ab63727150 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player c2506432-c159-30c0-93d8-64f2a6272277 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player a0a7ec06-d96a-3187-943b-79bf7e87b3a2 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 2737bbb4-080a-3670-89c1-780a32c86c5a in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 4941828d-27d9-3ea5-a0e2-8e881702c195 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player a205b8da-efc6-37ad-8e1d-84c0239cdd21 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player d944aee7-2684-33ce-b4ec-6dde00bdf73f in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 1ab04e7d-c966-3477-8a64-78d9a4421f72 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 2afb4ee9-7113-3f92-8544-46d36df86129 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 99963b1e-252d-3ba6-8126-9071dd762e6c in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 56a259bf-cc44-38c1-8061-dd05953c60e0 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player f2937d48-a72f-3375-bb6f-69c5f204d185 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 7d246c97-157b-31d3-a8fc-0b0f5f437b63 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 19487181-3859-391a-bf75-143cc8396d12 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 1dd34f6b-f553-3906-92e2-e13f78ae2b51 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 33e9440c-90db-3f3e-bf40-dab751114d4d in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 1acd01f1-1c8b-3758-959b-fdf4d16bd5de in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player fe412f9e-f508-30d0-b407-ff978cf8fc5c in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 77058a27-9ed9-3577-ba3a-f426b243548e in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 905847c0-6737-32d8-85e6-3f64ecd32033 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 5f452b45-a444-38e5-8393-5edeaa648813 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 043cbf82-02a9-3180-968e-af73411af080 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 3e1fbeae-13f8-3fbb-9842-63bc739da918 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 428a9eb8-eb59-3300-892c-1124ca047d38 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 3dc994a6-e4ba-3e8a-9bf9-4271ccca9810 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 9a9311e6-0e2a-3db9-849c-c74b23116910 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 8c6477df-589c-387e-8235-e07170c95726 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Mayor for player c97b21a8-25e6-37ce-8a04-8d5bcf6f4c4d in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 2737bbb4-080a-3670-89c1-780a32c86c5a in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 2ef1cb8d-614b-3e2e-9366-5d5cf65cf8bf in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 56a259bf-cc44-38c1-8061-dd05953c60e0 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 33e9440c-90db-3f3e-bf40-dab751114d4d in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 5f452b45-a444-38e5-8393-5edeaa648813 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 77058a27-9ed9-3577-ba3a-f426b243548e in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 9a9311e6-0e2a-3db9-849c-c74b23116910 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 3dc994a6-e4ba-3e8a-9bf9-4271ccca9810 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 19487181-3859-391a-bf75-143cc8396d12 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 8c6477df-589c-387e-8235-e07170c95726 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 2afb4ee9-7113-3f92-8544-46d36df86129 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 428a9eb8-eb59-3300-892c-1124ca047d38 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 0aee4775-e8b4-37ed-8049-6be96ef5de8e in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player a0a7ec06-d96a-3187-943b-79bf7e87b3a2 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player fe207da4-45aa-3a2a-bf67-72a700290388 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 492dc575-b72e-3d83-b2fd-33ab63727150 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player c2506432-c159-30c0-93d8-64f2a6272277 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player d944aee7-2684-33ce-b4ec-6dde00bdf73f in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player f2937d48-a72f-3375-bb6f-69c5f204d185 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 99963b1e-252d-3ba6-8126-9071dd762e6c in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 7d246c97-157b-31d3-a8fc-0b0f5f437b63 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 1dd34f6b-f553-3906-92e2-e13f78ae2b51 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 3e1fbeae-13f8-3fbb-9842-63bc739da918 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Mayor for player c97b21a8-25e6-37ce-8a04-8d5bcf6f4c4d in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 64579c2e-a410-37b0-995d-5aa98a8b0304 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player a240c974-e4f5-311c-82e7-f92bb39b2584 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player b6529468-5313-3ea4-bc60-3e6ea6cabccd in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player a205b8da-efc6-37ad-8e1d-84c0239cdd21 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 1acd01f1-1c8b-3758-959b-fdf4d16bd5de in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player fe412f9e-f508-30d0-b407-ff978cf8fc5c in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 1ab04e7d-c966-3477-8a64-78d9a4421f72 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 4941828d-27d9-3ea5-a0e2-8e881702c195 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 043cbf82-02a9-3180-968e-af73411af080 in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player b2d19156-5364-397f-8e33-abd769d6319d in town test
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 905847c0-6737-32d8-85e6-3f64ecd32033 in town test
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved settings to disk for town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSet 16 town settings for town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mRestored town settings for town test: {image=test_test1, maxPlayers=100.0, d42f4ef4-ff82-4347-a77c-d322040ff0b2={isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, 03e91d98-f031-4f21-b5f8-bb413f2cbf1b={isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, showTownInList=true, 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7={isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, c279a2aa-6379-4f5a-9ec5-bce65fa5d52a={maxPlayers=100.0, d42f4ef4-ff82-4347-a77c-d322040ff0b2={isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, 03e91d98-f031-4f21-b5f8-bb413f2cbf1b={isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, showTownInList=true, 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7={isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, c279a2aa-6379-4f5a-9ec5-bce65fa5d52a={maxPlayers=100.0, d42f4ef4-ff82-4347-a77c-d322040ff0b2={isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, 03e91d98-f031-4f21-b5f8-bb413f2cbf1b={isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, showTownInList=true, 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7={isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, c279a2aa-6379-4f5a-9ec5-bce65fa5d52a={maxPlayers=100.0, d42f4ef4-ff82-4347-a77c-d322040ff0b2={isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, 03e91d98-f031-4f21-b5f8-bb413f2cbf1b={isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, showTownInList=true, 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7={isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, c279a2aa-6379-4f5a-9ec5-bce65fa5d52a={maxPlayers=100.0, isOpen=true, showTownInList=true, enableTownChat=true, name=test, description=yeeeee, allowPublicBuilding=false, enablePvP=false, allowMobSpawning=true}, description=yeeeee, isOpen=true, enableTownChat=true, name=test, 6a557cc3-fb5f-427a-a926-bafe2d3a0a56={isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, allowPublicBuilding=false, enablePvP=false, allowMobSpawning=true}, description=yeeeee, isOpen=true, enableTownChat=true, name=test, 6a557cc3-fb5f-427a-a926-bafe2d3a0a56={isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, allowPublicBuilding=false, enablePvP=false, allowMobSpawning=true}, description=yeeeee, isOpen=true, enableTownChat=true, name=test, 6a557cc3-fb5f-427a-a926-bafe2d3a0a56={isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, allowPublicBuilding=false, enablePvP=false, allowMobSpawning=true}, description=yeeee, imageSettings={offsetX=0.0, offsetY=0.0, scale=1.0}, isOpen=true, enableTownChat=true, name=test, 6a557cc3-fb5f-427a-a926-bafe2d3a0a56={isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, allowPublicBuilding=false, enablePvP=false, allowMobSpawning=true}
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mLoaded town 'test' with 35 players from c279a2aa-6379-4f5a-9ec5-bce65fa5d52a.json
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mRestored 62 player-town relationships
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mLoaded 5 towns from individual files
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mTown data loading completed. Final town count in TownManager: 5
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mPerforming town settings health check during server startup
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mPerforming town settings health check...
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mTown settings health check complete: 5 checked, 0 repaired, 0 errors
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading town settings during server startup
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mLoaded town settings from disk: 5 successful, 0 errors
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying persisted settings to all loaded towns
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [31m[Server thread/ERROR][m [36m(pokecobbleclaim)[m [31mError applying town setting isOpen: Cannot invoke "net.minecraft.server.PlayerManager.getPlayerList()" because the return value of "net.minecraft.server.MinecraftServer.getPlayerManager()" is null
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mUnknown town setting: allowPublicBuilding
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [31m[Server thread/ERROR][m [36m(pokecobbleclaim)[m [31mError applying town setting allowPublicBuilding: Cannot invoke "net.minecraft.server.PlayerManager.getPlayerList()" because the return value of "net.minecraft.server.MinecraftServer.getPlayerManager()" is null
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mUnknown town setting: showTownInList
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [31m[Server thread/ERROR][m [36m(pokecobbleclaim)[m [31mError applying town setting showTownInList: Cannot invoke "net.minecraft.server.PlayerManager.getPlayerList()" because the return value of "net.minecraft.server.MinecraftServer.getPlayerManager()" is null
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mUnknown town setting: enablePvP
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [31m[Server thread/ERROR][m [36m(pokecobbleclaim)[m [31mError applying town setting enablePvP: Cannot invoke "net.minecraft.server.PlayerManager.getPlayerList()" because the return value of "net.minecraft.server.MinecraftServer.getPlayerManager()" is null
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mUnknown town setting: enableTownChat
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [31m[Server thread/ERROR][m [36m(pokecobbleclaim)[m [31mError applying town setting enableTownChat: Cannot invoke "net.minecraft.server.PlayerManager.getPlayerList()" because the return value of "net.minecraft.server.MinecraftServer.getPlayerManager()" is null
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mUnknown town setting: allowMobSpawning
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:02:29][m [31m[Server thread/ERROR][m [36m(pokecobbleclaim)[m [31mError applying town setting allowMobSpawning: Cannot invoke "net.minecraft.server.PlayerManager.getPlayerList()" because the return value of "net.minecraft.server.MinecraftServer.getPlayerManager()" is null
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [31m[Server thread/ERROR][m [36m(pokecobbleclaim)[m [31mError applying town setting isOpen: Cannot invoke "net.minecraft.server.PlayerManager.getPlayerList()" because the return value of "net.minecraft.server.MinecraftServer.getPlayerManager()" is null
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mUnknown town setting: allowPublicBuilding
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [31m[Server thread/ERROR][m [36m(pokecobbleclaim)[m [31mError applying town setting allowPublicBuilding: Cannot invoke "net.minecraft.server.PlayerManager.getPlayerList()" because the return value of "net.minecraft.server.MinecraftServer.getPlayerManager()" is null
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mUnknown town setting: showTownInList
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [31m[Server thread/ERROR][m [36m(pokecobbleclaim)[m [31mError applying town setting showTownInList: Cannot invoke "net.minecraft.server.PlayerManager.getPlayerList()" because the return value of "net.minecraft.server.MinecraftServer.getPlayerManager()" is null
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mUnknown town setting: enablePvP
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [31m[Server thread/ERROR][m [36m(pokecobbleclaim)[m [31mError applying town setting enablePvP: Cannot invoke "net.minecraft.server.PlayerManager.getPlayerList()" because the return value of "net.minecraft.server.MinecraftServer.getPlayerManager()" is null
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mUnknown town setting: enableTownChat
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [31m[Server thread/ERROR][m [36m(pokecobbleclaim)[m [31mError applying town setting enableTownChat: Cannot invoke "net.minecraft.server.PlayerManager.getPlayerList()" because the return value of "net.minecraft.server.MinecraftServer.getPlayerManager()" is null
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mUnknown town setting: allowMobSpawning
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:02:29][m [31m[Server thread/ERROR][m [36m(pokecobbleclaim)[m [31mError applying town setting allowMobSpawning: Cannot invoke "net.minecraft.server.PlayerManager.getPlayerList()" because the return value of "net.minecraft.server.MinecraftServer.getPlayerManager()" is null
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'test' to individual file: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a.json
[m[34m[05:02:29][m [31m[Server thread/ERROR][m [36m(pokecobbleclaim)[m [31mError applying town setting image: Cannot invoke "net.minecraft.server.PlayerManager.getPlayerList()" because the return value of "net.minecraft.server.MinecraftServer.getPlayerManager()" is null
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'test' to individual file: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a.json
[m[34m[05:02:29][m [31m[Server thread/ERROR][m [36m(pokecobbleclaim)[m [31mError applying town setting maxPlayers: Cannot invoke "net.minecraft.server.PlayerManager.getPlayerList()" because the return value of "net.minecraft.server.MinecraftServer.getPlayerManager()" is null
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mUnknown town setting: d42f4ef4-ff82-4347-a77c-d322040ff0b2
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'test' to individual file: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a.json
[m[34m[05:02:29][m [31m[Server thread/ERROR][m [36m(pokecobbleclaim)[m [31mError applying town setting d42f4ef4-ff82-4347-a77c-d322040ff0b2: Cannot invoke "net.minecraft.server.PlayerManager.getPlayerList()" because the return value of "net.minecraft.server.MinecraftServer.getPlayerManager()" is null
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mUnknown town setting: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'test' to individual file: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a.json
[m[34m[05:02:29][m [31m[Server thread/ERROR][m [36m(pokecobbleclaim)[m [31mError applying town setting 03e91d98-f031-4f21-b5f8-bb413f2cbf1b: Cannot invoke "net.minecraft.server.PlayerManager.getPlayerList()" because the return value of "net.minecraft.server.MinecraftServer.getPlayerManager()" is null
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mUnknown town setting: showTownInList
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'test' to individual file: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a.json
[m[34m[05:02:29][m [31m[Server thread/ERROR][m [36m(pokecobbleclaim)[m [31mError applying town setting showTownInList: Cannot invoke "net.minecraft.server.PlayerManager.getPlayerList()" because the return value of "net.minecraft.server.MinecraftServer.getPlayerManager()" is null
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mUnknown town setting: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'test' to individual file: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a.json
[m[34m[05:02:29][m [31m[Server thread/ERROR][m [36m(pokecobbleclaim)[m [31mError applying town setting 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7: Cannot invoke "net.minecraft.server.PlayerManager.getPlayerList()" because the return value of "net.minecraft.server.MinecraftServer.getPlayerManager()" is null
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mUnknown town setting: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'test' to individual file: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a.json
[m[34m[05:02:29][m [31m[Server thread/ERROR][m [36m(pokecobbleclaim)[m [31mError applying town setting c279a2aa-6379-4f5a-9ec5-bce65fa5d52a: Cannot invoke "net.minecraft.server.PlayerManager.getPlayerList()" because the return value of "net.minecraft.server.MinecraftServer.getPlayerManager()" is null
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'test' to individual file: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a.json
[m[34m[05:02:29][m [31m[Server thread/ERROR][m [36m(pokecobbleclaim)[m [31mError applying town setting description: Cannot invoke "net.minecraft.server.PlayerManager.getPlayerList()" because the return value of "net.minecraft.server.MinecraftServer.getPlayerManager()" is null
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'test' to individual file: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a.json
[m[34m[05:02:29][m [31m[Server thread/ERROR][m [36m(pokecobbleclaim)[m [31mError applying town setting imageSettings: Cannot invoke "net.minecraft.server.PlayerManager.getPlayerList()" because the return value of "net.minecraft.server.MinecraftServer.getPlayerManager()" is null
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'test' to individual file: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a.json
[m[34m[05:02:29][m [31m[Server thread/ERROR][m [36m(pokecobbleclaim)[m [31mError applying town setting isOpen: Cannot invoke "net.minecraft.server.PlayerManager.getPlayerList()" because the return value of "net.minecraft.server.MinecraftServer.getPlayerManager()" is null
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mUnknown town setting: enableTownChat
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'test' to individual file: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a.json
[m[34m[05:02:29][m [31m[Server thread/ERROR][m [36m(pokecobbleclaim)[m [31mError applying town setting enableTownChat: Cannot invoke "net.minecraft.server.PlayerManager.getPlayerList()" because the return value of "net.minecraft.server.MinecraftServer.getPlayerManager()" is null
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'test' to individual file: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a.json
[m[34m[05:02:29][m [31m[Server thread/ERROR][m [36m(pokecobbleclaim)[m [31mError applying town setting name: Cannot invoke "net.minecraft.server.PlayerManager.getPlayerList()" because the return value of "net.minecraft.server.MinecraftServer.getPlayerManager()" is null
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mUnknown town setting: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'test' to individual file: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a.json
[m[34m[05:02:29][m [31m[Server thread/ERROR][m [36m(pokecobbleclaim)[m [31mError applying town setting 6a557cc3-fb5f-427a-a926-bafe2d3a0a56: Cannot invoke "net.minecraft.server.PlayerManager.getPlayerList()" because the return value of "net.minecraft.server.MinecraftServer.getPlayerManager()" is null
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mUnknown town setting: allowPublicBuilding
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'test' to individual file: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a.json
[m[34m[05:02:29][m [31m[Server thread/ERROR][m [36m(pokecobbleclaim)[m [31mError applying town setting allowPublicBuilding: Cannot invoke "net.minecraft.server.PlayerManager.getPlayerList()" because the return value of "net.minecraft.server.MinecraftServer.getPlayerManager()" is null
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mUnknown town setting: enablePvP
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'test' to individual file: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a.json
[m[34m[05:02:29][m [31m[Server thread/ERROR][m [36m(pokecobbleclaim)[m [31mError applying town setting enablePvP: Cannot invoke "net.minecraft.server.PlayerManager.getPlayerList()" because the return value of "net.minecraft.server.MinecraftServer.getPlayerManager()" is null
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mUnknown town setting: allowMobSpawning
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'test' to individual file: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a.json
[m[34m[05:02:29][m [31m[Server thread/ERROR][m [36m(pokecobbleclaim)[m [31mError applying town setting allowMobSpawning: Cannot invoke "net.minecraft.server.PlayerManager.getPlayerList()" because the return value of "net.minecraft.server.MinecraftServer.getPlayerManager()" is null
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [31m[Server thread/ERROR][m [36m(pokecobbleclaim)[m [31mError applying town setting isOpen: Cannot invoke "net.minecraft.server.PlayerManager.getPlayerList()" because the return value of "net.minecraft.server.MinecraftServer.getPlayerManager()" is null
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mUnknown town setting: allowPublicBuilding
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [31m[Server thread/ERROR][m [36m(pokecobbleclaim)[m [31mError applying town setting allowPublicBuilding: Cannot invoke "net.minecraft.server.PlayerManager.getPlayerList()" because the return value of "net.minecraft.server.MinecraftServer.getPlayerManager()" is null
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mUnknown town setting: showTownInList
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [31m[Server thread/ERROR][m [36m(pokecobbleclaim)[m [31mError applying town setting showTownInList: Cannot invoke "net.minecraft.server.PlayerManager.getPlayerList()" because the return value of "net.minecraft.server.MinecraftServer.getPlayerManager()" is null
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mUnknown town setting: enablePvP
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [31m[Server thread/ERROR][m [36m(pokecobbleclaim)[m [31mError applying town setting enablePvP: Cannot invoke "net.minecraft.server.PlayerManager.getPlayerList()" because the return value of "net.minecraft.server.MinecraftServer.getPlayerManager()" is null
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mUnknown town setting: enableTownChat
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [31m[Server thread/ERROR][m [36m(pokecobbleclaim)[m [31mError applying town setting enableTownChat: Cannot invoke "net.minecraft.server.PlayerManager.getPlayerList()" because the return value of "net.minecraft.server.MinecraftServer.getPlayerManager()" is null
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mUnknown town setting: allowMobSpawning
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:02:29][m [31m[Server thread/ERROR][m [36m(pokecobbleclaim)[m [31mError applying town setting allowMobSpawning: Cannot invoke "net.minecraft.server.PlayerManager.getPlayerList()" because the return value of "net.minecraft.server.MinecraftServer.getPlayerManager()" is null
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [31m[Server thread/ERROR][m [36m(pokecobbleclaim)[m [31mError applying town setting isOpen: Cannot invoke "net.minecraft.server.PlayerManager.getPlayerList()" because the return value of "net.minecraft.server.MinecraftServer.getPlayerManager()" is null
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mUnknown town setting: allowPublicBuilding
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [31m[Server thread/ERROR][m [36m(pokecobbleclaim)[m [31mError applying town setting allowPublicBuilding: Cannot invoke "net.minecraft.server.PlayerManager.getPlayerList()" because the return value of "net.minecraft.server.MinecraftServer.getPlayerManager()" is null
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mUnknown town setting: showTownInList
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [31m[Server thread/ERROR][m [36m(pokecobbleclaim)[m [31mError applying town setting showTownInList: Cannot invoke "net.minecraft.server.PlayerManager.getPlayerList()" because the return value of "net.minecraft.server.MinecraftServer.getPlayerManager()" is null
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mUnknown town setting: enablePvP
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [31m[Server thread/ERROR][m [36m(pokecobbleclaim)[m [31mError applying town setting enablePvP: Cannot invoke "net.minecraft.server.PlayerManager.getPlayerList()" because the return value of "net.minecraft.server.MinecraftServer.getPlayerManager()" is null
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mUnknown town setting: enableTownChat
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [31m[Server thread/ERROR][m [36m(pokecobbleclaim)[m [31mError applying town setting enableTownChat: Cannot invoke "net.minecraft.server.PlayerManager.getPlayerList()" because the return value of "net.minecraft.server.MinecraftServer.getPlayerManager()" is null
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(pokecobbleclaim)[m [0mUnknown town setting: allowMobSpawning
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:02:29][m [31m[Server thread/ERROR][m [36m(pokecobbleclaim)[m [31mError applying town setting allowMobSpawning: Cannot invoke "net.minecraft.server.PlayerManager.getPlayerList()" because the return value of "net.minecraft.server.MinecraftServer.getPlayerManager()" is null
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mApplied persisted settings to 5 towns
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mPreparing town data synchronization for 5 loaded towns
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mTown settings status: 5 total, 5 loaded from disk, 5 with custom settings out of 5 towns
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSynchronized all loaded town settings to online players
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mReset all town and player data versions
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(Minecraft)[m [0mStarting minecraft server version 1.20.1
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(Minecraft)[m [0mLoading properties
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(Minecraft)[m [0mDefault game type: SURVIVAL
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(Minecraft)[m [0mGenerating keypair
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(Minecraft)[m [0mStarting Minecraft server on *:25565
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(Minecraft)[m [0mUsing epoll channel type
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(Minecraft)[m [0m**** SERVER IS RUNNING IN OFFLINE/INSECURE MODE!
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(Minecraft)[m [0mThe server will make no attempt to authenticate usernames. Beware.
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(Minecraft)[m [0mWhile this makes the game possible to play without internet access, it also opens up the ability for hackers to connect with any username they choose.
[m[34m[05:02:29][m [33m[Server thread/WARN][m [36m(Minecraft)[m [0mTo change this, set "online-mode" to "true" in the server.properties file.
[m[34m[05:02:29][m [32m[Server thread/INFO][m [36m(Minecraft)[m [0mPreparing level "world"
[m[34m[05:02:31][m [32m[Server thread/INFO][m [36m(Minecraft)[m [0mPreparing start region for dimension minecraft:overworld
[m[34m[05:02:33][m [32m[Worker-Main-10/INFO][m [36m(Minecraft)[m [0mPreparing spawn area: 0%
[m[34m[05:02:33][m [32m[Worker-Main-10/INFO][m [36m(Minecraft)[m [0mPreparing spawn area: 0%
[m[34m[05:02:33][m [32m[Worker-Main-10/INFO][m [36m(Minecraft)[m [0mPreparing spawn area: 0%
[m[34m[05:02:33][m [32m[Worker-Main-10/INFO][m [36m(Minecraft)[m [0mPreparing spawn area: 0%
[m[34m[05:02:33][m [32m[Worker-Main-10/INFO][m [36m(Minecraft)[m [0mPreparing spawn area: 0%
[m[34m[05:02:33][m [32m[Worker-Main-10/INFO][m [36m(Minecraft)[m [0mPreparing spawn area: 0%
[m[34m[05:02:34][m [32m[Worker-Main-11/INFO][m [36m(Minecraft)[m [0mPreparing spawn area: 4%
[m[34m[05:02:34][m [32m[Server thread/INFO][m [36m(Minecraft)[m [0mTime elapsed: 3377 ms
[m[34m[05:02:34][m [32m[Server thread/INFO][m [36m(Minecraft)[m [0mDone (4.649s)! For help, type "help"
[m[34m[05:02:36][m [32m[Server thread/INFO][m [36m(Minecraft)[m [0mPlayer457[/127.0.0.1:43690] logged in with entity id 177 at (-0.5, 76.0, -6.5)
[m[34m[05:02:36][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mCreated new player data for Player457
[m[34m[05:02:36][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSent complete town data to new player Player457 (5 towns)
[m[34m[05:02:36][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mPlayer Player457 is not in any town
[m[34m[05:02:36][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSyncing settings for 5 towns to player 324bf773-41c1-3c88-8191-03f2982049b1
[m[34m[05:02:36][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSuccessfully synced all town settings to player 324bf773-41c1-3c88-8191-03f2982049b1
[m[34m[05:02:36][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSynchronized all town settings for player Player457 (not in any town)
[m[34m[05:02:36][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSynchronized 5 towns to player Player457
[m[34m[05:02:36][m [32m[Server thread/INFO][m [36m(Minecraft)[m [0mPlayer457 joined the game
[m[34m[05:02:39][m [32m[Netty Epoll Server IO #2/INFO][m [36m(pokecobbleclaim)[m [0mSending town list to player Player457 with 5 towns
[m[34m[05:02:39][m [32m[Netty Epoll Server IO #2/INFO][m [36m(pokecobbleclaim)[m [0mTowns being sent: aaaaaaaaaaaaa, TestSettingsPersistence, test, aaaaaaaaaa, TestSettingsPersistence, 
[m[34m[05:02:41][m [32m[Netty Epoll Server IO #2/INFO][m [36m(pokecobbleclaim)[m [0mProcessing town join request from player Player457 for town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a
[m[34m[05:02:41][m [32m[Netty Epoll Server IO #2/INFO][m [36m(pokecobbleclaim)[m [0mFound town: test (players: 35/100, type: OPEN)
[m[34m[05:02:41][m [32m[Netty Epoll Server IO #2/INFO][m [36m(pokecobbleclaim)[m [0mAttempting to add player Player457 to town test
[m[34m[05:02:41][m [32m[Netty Epoll Server IO #2/INFO][m [36m(pokecobbleclaim)[m [0mCreating TownPlayer: Player457 (324bf773-41c1-3c88-8191-03f2982049b1) with rank MEMBER for town test
[m[34m[05:02:41][m [32m[Netty Epoll Server IO #2/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'test' to individual file: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a.json
[m[34m[05:02:41][m [32m[Netty Epoll Server IO #2/INFO][m [36m(pokecobbleclaim)[m [0mBroadcasted town list update to 1 players (5 towns)
[m[34m[05:02:41][m [32m[Netty Epoll Server IO #2/INFO][m [36m(pokecobbleclaim)[m [0mHandled player town membership change: joined town test for player 324bf773-41c1-3c88-8191-03f2982049b1
[m[34m[05:02:41][m [32m[Netty Epoll Server IO #2/INFO][m [36m(pokecobbleclaim)[m [0mBroadcasted player list update for town test to 1 players
[m[34m[05:02:41][m [32m[Netty Epoll Server IO #2/INFO][m [36m(pokecobbleclaim)[m [0mBroadcasted town list update to 1 players (5 towns)
[m[34m[05:02:41][m [32m[Netty Epoll Server IO #2/INFO][m [36m(pokecobbleclaim)[m [0mPlayer 324bf773-41c1-3c88-8191-03f2982049b1 joined town test (now 36 players)
[m[34m[05:02:41][m [32m[Netty Epoll Server IO #2/INFO][m [36m(pokecobbleclaim)[m [0mSUCCESS: Player Player457 joined town: test
[m[34m[05:02:41][m [32m[Server thread/INFO][m [36m(pokecobbleclaim)[m [0mSynchronized town data for joined town test to player Player457
[m[34m[05:02:44][m [32m[Netty Epoll Server IO #2/INFO][m [36m(pokecobbleclaim)[m [0mSending town list to player Player457 with 5 towns
[m[34m[05:02:44][m [32m[Netty Epoll Server IO #2/INFO][m [36m(pokecobbleclaim)[m [0mTowns being sent: aaaaaaaaaaaaa, TestSettingsPersistence, test, aaaaaaaaaa, TestSettingsPersistence, 
[m