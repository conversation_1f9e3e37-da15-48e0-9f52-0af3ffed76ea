package com.pokecobble.town.network.town;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.TownPlayer;
import com.pokecobble.town.network.NetworkConstants;
import com.pokecobble.town.network.NetworkManager;
import com.pokecobble.town.network.PacketValidator;
import com.pokecobble.town.util.TownImageUtil;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.networking.v1.PacketByteBufs;
import net.fabricmc.fabric.api.networking.v1.PacketSender;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.util.Identifier;

import java.util.*;

/**
 * Handles synchronization of town image data between server and clients.
 * This class is responsible for efficiently sending town image updates to the appropriate players.
 */
public class TownImageSynchronizer {
    // Constants for town image update packets
    public static final Identifier TOWN_IMAGE_UPDATE = new Identifier(NetworkConstants.MOD_ID, "town_image_update");
    public static final Identifier TOWN_IMAGE_SETTINGS_UPDATE = new Identifier(NetworkConstants.MOD_ID, "town_image_settings_update");
    public static final Identifier REQUEST_TOWN_IMAGE_UPDATE = new Identifier(NetworkConstants.MOD_ID, "request_town_image_update");

    // Cache to track which players have which version of town image data
    private static final Map<UUID, Map<UUID, Integer>> PLAYER_TOWN_IMAGE_VERSIONS = new HashMap<>();

    /**
     * Registers client-side packet handlers for town image synchronization.
     */
    @Environment(EnvType.CLIENT)
    public static void registerClientHandlers() {
        // Register town image update handler
        net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.registerGlobalReceiver(
                TOWN_IMAGE_UPDATE,
                TownImageSynchronizer::handleTownImageUpdate
        );

        // Register town image settings update handler
        net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.registerGlobalReceiver(
                TOWN_IMAGE_SETTINGS_UPDATE,
                TownImageSynchronizer::handleTownImageSettingsUpdate
        );
    }

    /**
     * Registers server-side packet handlers for town image synchronization.
     */
    public static void registerServerHandlers() {
        // Register request town image update handler
        net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking.registerGlobalReceiver(
                REQUEST_TOWN_IMAGE_UPDATE,
                TownImageSynchronizer::handleRequestTownImageUpdate
        );

        // Register town image settings update handler (client to server)
        net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking.registerGlobalReceiver(
                TOWN_IMAGE_SETTINGS_UPDATE,
                TownImageSynchronizer::handleClientImageSettingsUpdate
        );
    }

    /**
     * Requests town image data from the server.
     * This should be called when a client needs to refresh town image data.
     *
     * @param townId The ID of the town to request data for, or null for all towns
     */
    @Environment(EnvType.CLIENT)
    public static void requestTownImageUpdate(UUID townId) {
        try {
            // Create packet buffer
            PacketByteBuf buf = PacketByteBufs.create();

            // Write town ID (or null for all towns)
            if (townId != null) {
                buf.writeBoolean(true);
                buf.writeUuid(townId);
            } else {
                buf.writeBoolean(false);
            }

            // Send packet to server
            net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.send(REQUEST_TOWN_IMAGE_UPDATE, buf);

            Pokecobbleclaim.LOGGER.debug("Requested town image update for " + (townId != null ? townId : "all towns"));
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error requesting town image update: " + e.getMessage());
        }
    }

    /**
     * Handles requests for town image updates on the server side.
     */
    private static void handleRequestTownImageUpdate(MinecraftServer server, ServerPlayerEntity player,
                                                   net.minecraft.server.network.ServerPlayNetworkHandler handler,
                                                   PacketByteBuf buf, PacketSender sender) {
        try {
            // Validate packet size
            PacketValidator.validatePacketSize(buf);

            // Read if request is for a specific town
            boolean isSpecificTown = buf.readBoolean();

            if (isSpecificTown) {
                // Read town ID
                UUID townId = buf.readUuid();

                // Get town
                Town town = TownManager.getInstance().getTownById(townId);
                if (town != null) {
                    // Send town image update
                    sendTownImageUpdate(player, town);

                    // Send town image settings update safely
                    String imageName = town.getImage();
                    if (imageName != null && !imageName.isEmpty() && !imageName.equals("default")) {
                        sendTownImageSettingsUpdateSafe(player, town, imageName);
                    }
                }
            } else {
                // Send updates for all towns the player should know about
                for (Town town : TownManager.getInstance().getAllTowns()) {
                    if (shouldSendTownImageUpdate(player.getUuid(), town)) {
                        // Send town image update
                        sendTownImageUpdate(player, town);

                        // Send town image settings update safely
                        String imageName = town.getImage();
                        if (imageName != null && !imageName.isEmpty() && !imageName.equals("default")) {
                            sendTownImageSettingsUpdateSafe(player, town, imageName);
                        }
                    }
                }
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling town image update request: " + e.getMessage());
        }
    }

    /**
     * Synchronizes town image data to all relevant players.
     * This method efficiently sends only the changed data to the players who need it.
     *
     * @param server The server instance
     * @param town The town to synchronize
     */
    public static void syncTownImageData(MinecraftServer server, Town town) {
        if (town == null) {
            return;
        }

        // Check if image data has changed
        if (!town.hasChanged(Town.ASPECT_IMAGE)) {
            return;
        }

        // Get all online players
        List<ServerPlayerEntity> onlinePlayers = server.getPlayerManager().getPlayerList();

        // Determine which players need updates
        for (ServerPlayerEntity player : onlinePlayers) {
            UUID playerId = player.getUuid();

            // Check if this player needs an update for this town
            if (shouldSendTownImageUpdate(playerId, town)) {
                // Send town image update
                sendTownImageUpdate(player, town);

                // Update the player's known version for this town's image
                updatePlayerTownImageVersion(playerId, town.getId(), town.getDataVersion());
            }
        }
    }

    /**
     * Forces town image data sync to a specific player.
     * This method sends image data regardless of change status, useful for player join events.
     *
     * @param player The player to send data to
     * @param town The town to synchronize
     */
    public static void forceTownImageSyncToPlayer(ServerPlayerEntity player, Town town) {
        if (player == null || town == null) {
            return;
        }

        try {
            // Send town image update
            sendTownImageUpdate(player, town);

            // Send town image settings update only if the town has an image
            String imageName = town.getImage();
            if (imageName != null && !imageName.isEmpty() && !imageName.equals("default")) {
                // Use the safer method that doesn't trigger client-side class loading
                sendTownImageSettingsUpdateSafe(player, town, imageName);
            }

            // Update the player's known version for this town's image
            updatePlayerTownImageVersion(player.getUuid(), town.getId(), town.getDataVersion());

            Pokecobbleclaim.LOGGER.debug("Forced town image sync for " + town.getName() + " to " + player.getName().getString());
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error forcing town image sync to player: " + e.getMessage());
        }
    }

    /**
     * Synchronizes town image settings to all relevant players.
     * This should be called when a town's image settings change.
     *
     * @param server The server instance
     * @param town The town whose image settings have changed
     * @param imageName The name of the image whose settings have changed
     */
    public static void syncTownImageSettings(MinecraftServer server, Town town, String imageName) {
        if (town == null || imageName == null || imageName.isEmpty()) {
            return;
        }

        // Get all online players
        List<ServerPlayerEntity> onlinePlayers = server.getPlayerManager().getPlayerList();

        // Send updates to relevant players
        for (ServerPlayerEntity player : onlinePlayers) {
            // Check if this player should receive updates for this town
            if (shouldSendTownImageUpdate(player.getUuid(), town)) {
                // Send town image settings update safely
                sendTownImageSettingsUpdateSafe(player, town, imageName);
            }
        }
    }

    /**
     * Checks if a player should receive updates for a town's image.
     *
     * @param playerId The player's UUID
     * @param town The town
     * @return True if the player should receive updates, false otherwise
     */
    private static boolean shouldSendTownImageUpdate(UUID playerId, Town town) {
        // Players should receive updates for:
        // 1. Their own town
        // 2. Towns they have visited or interacted with

        // Check if player is in the town
        if (town.getPlayers().contains(playerId)) {
            return true;
        }

        // Check if player has a different version than the current one
        Map<UUID, Integer> playerVersions = PLAYER_TOWN_IMAGE_VERSIONS.get(playerId);
        if (playerVersions != null) {
            Integer knownVersion = playerVersions.get(town.getId());
            if (knownVersion == null || knownVersion < town.getDataVersion()) {
                return true;
            }
        }

        // By default, don't send updates
        return false;
    }

    /**
     * Updates a player's known version for a town's image.
     *
     * @param playerId The player's UUID
     * @param townId The town's UUID
     * @param version The new version
     */
    private static void updatePlayerTownImageVersion(UUID playerId, UUID townId, int version) {
        // Get or create player versions map
        Map<UUID, Integer> playerVersions = PLAYER_TOWN_IMAGE_VERSIONS.computeIfAbsent(playerId, k -> new HashMap<>());

        // Update version
        playerVersions.put(townId, version);
    }

    /**
     * Sends town image data to a player.
     *
     * @param player The player to send data to
     * @param town The town
     */
    private static void sendTownImageUpdate(ServerPlayerEntity player, Town town) {
        try {
            // Create packet buffer
            PacketByteBuf buf = NetworkManager.createPacket();

            // Write town UUID
            buf.writeUuid(town.getId());

            // Write town data version
            buf.writeInt(town.getDataVersion());

            // Write town image name
            String imageName = town.getImage();
            buf.writeString(imageName != null ? imageName : "default", NetworkConstants.MAX_STRING_LENGTH);

            // Send packet to player
            NetworkManager.sendToPlayer(player, TOWN_IMAGE_UPDATE, buf);

            Pokecobbleclaim.LOGGER.debug("Sent town image update for " + town.getName() + " to " + player.getName().getString());
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending town image update: " + e.getMessage());
        }
    }

    /**
     * Sends town image settings to a player.
     *
     * @param player The player to send data to
     * @param town The town
     */
    private static void sendTownImageSettingsUpdate(ServerPlayerEntity player, Town town) {
        // Get the current image name
        String imageName = town.getImage();
        if (imageName == null || imageName.isEmpty() || imageName.equals("default")) {
            return;
        }

        // Send settings for the current image safely
        sendTownImageSettingsUpdateSafe(player, town, imageName);
    }

    /**
     * Safely sends settings for a specific town image to a player.
     * This method avoids client-side class loading issues by getting settings directly from town settings.
     *
     * @param player The player to send data to
     * @param town The town
     * @param imageName The name of the image
     */
    private static void sendTownImageSettingsUpdateSafe(ServerPlayerEntity player, Town town, String imageName) {
        try {
            // Get image settings directly from town settings to avoid client-side class loading
            Map<String, Object> townSettings = com.pokecobble.town.config.TownSettingsManager.getTownSettings(town.getId());
            if (townSettings == null || !townSettings.containsKey("imageSettings")) {
                // No settings to send
                return;
            }

            Object imageSettingsObj = townSettings.get("imageSettings");
            if (!(imageSettingsObj instanceof Map)) {
                return;
            }

            @SuppressWarnings("unchecked")
            Map<String, Object> imageSettingsMap = (Map<String, Object>) imageSettingsObj;

            float scale = 1.0f;
            int offsetX = 0;
            int offsetY = 0;

            if (imageSettingsMap.containsKey("scale")) {
                scale = ((Number) imageSettingsMap.get("scale")).floatValue();
            }
            if (imageSettingsMap.containsKey("offsetX")) {
                offsetX = ((Number) imageSettingsMap.get("offsetX")).intValue();
            }
            if (imageSettingsMap.containsKey("offsetY")) {
                offsetY = ((Number) imageSettingsMap.get("offsetY")).intValue();
            }

            // Create packet buffer
            PacketByteBuf buf = NetworkManager.createPacket();

            // Write town UUID
            buf.writeUuid(town.getId());

            // Write image name
            buf.writeString(imageName, NetworkConstants.MAX_STRING_LENGTH);

            // Write settings
            buf.writeFloat(scale);
            buf.writeInt(offsetX);
            buf.writeInt(offsetY);

            // Send packet to player
            NetworkManager.sendToPlayer(player, TOWN_IMAGE_SETTINGS_UPDATE, buf);

            Pokecobbleclaim.LOGGER.debug("Sent town image settings update (safe) for " + town.getName() + ":" + imageName + " to " + player.getName().getString());
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending town image settings update (safe): " + e.getMessage());
        }
    }

    /**
     * Sends settings for a specific town image to a player.
     *
     * @param player The player to send data to
     * @param town The town
     * @param imageName The name of the image
     */
    private static void sendTownImageSettingsUpdate(ServerPlayerEntity player, Town town, String imageName) {
        try {
            // Get image settings
            TownImageUtil.ImageSettings settings = TownImageUtil.getImageSettings(town, imageName);
            if (settings == null) {
                // No settings to send
                return;
            }

            // Create packet buffer
            PacketByteBuf buf = NetworkManager.createPacket();

            // Write town UUID
            buf.writeUuid(town.getId());

            // Write image name
            buf.writeString(imageName, NetworkConstants.MAX_STRING_LENGTH);

            // Write settings
            buf.writeFloat(settings.scale);
            buf.writeInt(settings.offsetX);
            buf.writeInt(settings.offsetY);

            // Send packet to player
            NetworkManager.sendToPlayer(player, TOWN_IMAGE_SETTINGS_UPDATE, buf);

            Pokecobbleclaim.LOGGER.debug("Sent town image settings update for " + town.getName() + ":" + imageName + " to " + player.getName().getString());
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending town image settings update: " + e.getMessage());
        }
    }

    /**
     * Handles town image update packets on the client side.
     */
    @Environment(EnvType.CLIENT)
    private static void handleTownImageUpdate(net.minecraft.client.MinecraftClient client,
                                             net.minecraft.client.network.ClientPlayNetworkHandler handler,
                                             PacketByteBuf buf,
                                             PacketSender sender) {
        try {
            // Read town UUID
            UUID townId = buf.readUuid();

            // Read town data version
            int dataVersion = buf.readInt();

            // Read town image name
            String imageName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);

            // Get or create town
            Town town = TownManager.getInstance().getTownById(townId);
            if (town == null) {
                Pokecobbleclaim.LOGGER.warn("Received image update for unknown town: " + townId);
                return;
            }

            // Update town image
            town.setImage(imageName);

            // Set data version
            town.setDataVersion(dataVersion);

            // Clear changed aspects since we just updated
            town.clearChangedAspects();

            Pokecobbleclaim.LOGGER.debug("Received town image update for " + town.getName() + ": " + imageName);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling town image update: " + e.getMessage());
        }
    }

    /**
     * Handles town image settings update packets on the client side.
     */
    @Environment(EnvType.CLIENT)
    private static void handleTownImageSettingsUpdate(net.minecraft.client.MinecraftClient client,
                                                    net.minecraft.client.network.ClientPlayNetworkHandler handler,
                                                    PacketByteBuf buf,
                                                    PacketSender sender) {
        try {
            // Read town UUID
            UUID townId = buf.readUuid();

            // Read image name
            String imageName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);

            // Read settings
            float scale = buf.readFloat();
            int offsetX = buf.readInt();
            int offsetY = buf.readInt();

            // Get town
            Town town = TownManager.getInstance().getTownById(townId);
            if (town == null) {
                Pokecobbleclaim.LOGGER.warn("Received image settings update for unknown town: " + townId);
                return;
            }

            // Apply settings locally
            TownImageUtil.applyImageSettingsLocally(town, imageName, scale, offsetX, offsetY);

            Pokecobbleclaim.LOGGER.debug("Received town image settings update for " + town.getName() + ":" + imageName);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling town image settings update: " + e.getMessage());
        }
    }

    /**
     * Handles client-to-server town image settings update packets.
     * This is called when a client sends updated image settings to the server.
     */
    private static void handleClientImageSettingsUpdate(MinecraftServer server, ServerPlayerEntity player,
                                                      net.minecraft.server.network.ServerPlayNetworkHandler handler,
                                                      PacketByteBuf buf, PacketSender sender) {
        try {
            // Validate packet size
            PacketValidator.validatePacketSize(buf);

            // Read town UUID
            UUID townId = buf.readUuid();

            // Read image name
            String imageName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);

            // Read settings
            float scale = buf.readFloat();
            int offsetX = buf.readInt();
            int offsetY = buf.readInt();

            // Validate parameters
            scale = Math.max(0.1f, Math.min(scale, 5.0f)); // Limit scale between 0.1 and 5.0
            offsetX = Math.max(-500, Math.min(offsetX, 500)); // Limit offset between -500 and 500
            offsetY = Math.max(-500, Math.min(offsetY, 500)); // Limit offset between -500 and 500

            // Get town
            Town town = TownManager.getInstance().getTownById(townId);
            if (town == null) {
                Pokecobbleclaim.LOGGER.warn("Received image settings update for unknown town: " + townId);
                return;
            }

            // Security check: Verify that the player is in the town and has permission to change town settings
            if (!town.getPlayers().contains(player.getUuid())) {
                Pokecobbleclaim.LOGGER.warn("Player " + player.getName().getString() + " attempted to update image settings for town they're not in: " + town.getName());
                return;
            }

            // Check if player has permission to change town settings
            TownPlayer townPlayer = town.getPlayer(player.getUuid());
            if (townPlayer == null || (!townPlayer.hasPermission("Town Settings", "Can change town image") &&
                                      townPlayer.getRank() != com.pokecobble.town.TownPlayerRank.OWNER)) {
                Pokecobbleclaim.LOGGER.warn("Player " + player.getName().getString() + " attempted to update image settings without permission");
                return;
            }

            // Update town image settings
            TownImageUtil.saveImageSettings(town, imageName, scale, offsetX, offsetY);

            // Sync to all relevant clients
            syncTownImageSettings(server, town, imageName);

            Pokecobbleclaim.LOGGER.info("Player " + player.getName().getString() + " updated image settings for " + town.getName() + ":" + imageName);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling client image settings update: " + e.getMessage());
        }
    }

    /**
     * Clears a player's town image version cache.
     * This should be called when a player logs out.
     *
     * @param playerId The player's UUID
     */
    public static void clearPlayerCache(UUID playerId) {
        PLAYER_TOWN_IMAGE_VERSIONS.remove(playerId);
    }
}
