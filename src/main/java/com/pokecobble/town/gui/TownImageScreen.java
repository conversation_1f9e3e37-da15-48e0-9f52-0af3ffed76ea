package com.pokecobble.town.gui;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.network.town.TownImageSynchronizer;
import com.pokecobble.town.sound.SoundUtil;
import com.pokecobble.town.util.TownImageUtil;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.texture.NativeImage;
import net.minecraft.client.texture.NativeImageBackedTexture;
import net.minecraft.text.Text;
import net.minecraft.text.Style;
import net.minecraft.util.Formatting;
import net.minecraft.util.Identifier;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * Optimized screen for selecting a town image.
 */
public class TownImageScreen extends Screen {
    private final Screen parent;
    private final Town town;
    private List<String> availableImages = new ArrayList<>();
    private int selectedImageIndex = -1;
    private int scrollOffset = 0;
    private static final int IMAGE_SIZE = 100;
    private static final int IMAGE_PADDING = 20;
    private static final int VISIBLE_IMAGES = 3; // Number of images visible at once
    private int maxScrollOffset = 0; // Maximum scroll offset

    // Caching and optimization
    private static final Map<String, Identifier> CIRCULAR_MASK_CACHE = new HashMap<>();
    private static final Map<String, Long> LAST_IMAGE_UPDATE_TIME = new HashMap<>();
    private static final long IMAGE_UPDATE_COOLDOWN_MS = 5000; // 5 seconds cooldown between updates
    private boolean imagesLoaded = false;
    private boolean needsImageRefresh = true;
    private static final Executor IMAGE_LOADING_EXECUTOR = Executors.newSingleThreadExecutor();

    // Pre-calculated values for rendering
    private int panelWidth;
    private int panelHeight;
    private int panelX;
    private int panelY;
    private int totalWidth;
    private int startX;
    private int centerY;
    private int startIndex;

    // UI colors
    private static final int BACKGROUND_COLOR = 0xCC000000;
    private static final int HEADER_COLOR = 0xFF1E88E5;
    private static final int BORDER_COLOR = 0xFF42A5F5;
    private static final int SELECTED_COLOR = 0xFF4CAF50;
    private static final int UNSELECTED_COLOR = 0xFF333333;

    // Path to town images
    private static final String TOWN_IMAGES_PATH = "config/pokecobbleclaim/towns/";

    // Circular mask identifiers
    private static Identifier CIRCLE_MASK_IDENTIFIER;

    public TownImageScreen(Screen parent) {
        super(Text.translatable("screen.pokecobbleclaim.town_image"));
        this.parent = parent;

        // Get the player's town from ClientTownManager (client-side)
        this.town = com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTown();

        // Initialize the circular mask if not already created
        if (CIRCLE_MASK_IDENTIFIER == null) {
            createCircleMask();
        }
    }

    /**
     * Creates a circular mask texture that can be reused for all circular images
     */
    private void createCircleMask() {
        try {
            // Create a 128x128 circular mask image
            NativeImage maskImage = new NativeImage(128, 128, false);

            // Fill with transparent black
            for (int x = 0; x < 128; x++) {
                for (int y = 0; y < 128; y++) {
                    maskImage.setColor(x, y, 0x00000000);
                }
            }

            // Draw a white circle
            int centerX = 64;
            int centerY = 64;
            int radius = 64;
            int radiusSquared = radius * radius;

            for (int x = 0; x < 128; x++) {
                for (int y = 0; y < 128; y++) {
                    int dx = x - centerX;
                    int dy = y - centerY;
                    if (dx * dx + dy * dy <= radiusSquared) {
                        maskImage.setColor(x, y, 0xFFFFFFFF);
                    }
                }
            }

            // Create a texture from the mask
            NativeImageBackedTexture texture = new NativeImageBackedTexture(maskImage);

            // Register the texture
            CIRCLE_MASK_IDENTIFIER = MinecraftClient.getInstance().getTextureManager()
                .registerDynamicTexture("circle_mask", texture);

            com.pokecobble.Pokecobbleclaim.LOGGER.info("Created circular mask texture");
        } catch (Exception e) {
            com.pokecobble.Pokecobbleclaim.LOGGER.error("Failed to create circular mask: " + e.getMessage());
        }
    }

    @Override
    protected void init() {
        super.init();

        // Pre-calculate panel dimensions
        panelWidth = Math.max(600, width - 100);
        panelHeight = height - 60;
        panelX = (width - panelWidth) / 2;
        panelY = 30;
        centerY = panelY + (panelHeight / 2);

        // Only request town image data if needed (throttle requests)
        if (town != null) {
            String cacheKey = town.getId().toString();
            long currentTime = System.currentTimeMillis();
            Long lastUpdateTime = LAST_IMAGE_UPDATE_TIME.get(cacheKey);

            if (lastUpdateTime == null || currentTime - lastUpdateTime > IMAGE_UPDATE_COOLDOWN_MS) {
                TownImageSynchronizer.requestTownImageUpdate(town.getId());
                LAST_IMAGE_UPDATE_TIME.put(cacheKey, currentTime);
            }
        }

        // Load available images asynchronously
        if (!imagesLoaded || needsImageRefresh) {
            CompletableFuture.runAsync(() -> {
                loadAvailableImages();
                imagesLoaded = true;
                needsImageRefresh = false;

                // Calculate maximum scroll offset
                updateMaxScrollOffset();

                // Pre-calculate rendering values
                calculateRenderingValues();
            }, IMAGE_LOADING_EXECUTOR);
        } else {
            // Just update the maximum scroll offset and rendering values
            updateMaxScrollOffset();
            calculateRenderingValues();
        }
    }

    /**
     * Pre-calculates values used for rendering to avoid recalculating every frame
     */
    private void calculateRenderingValues() {
        // Calculate how many images to display
        int imagesToDisplay = Math.min(VISIBLE_IMAGES, availableImages.size());

        // Calculate total width based on actual number of images to display
        totalWidth = imagesToDisplay * (IMAGE_SIZE + IMAGE_PADDING) - IMAGE_PADDING;
        startX = (width - totalWidth) / 2;

        // Calculate starting index for centering
        if (availableImages.size() <= imagesToDisplay) {
            // If we have fewer images than visible slots, start from the beginning
            startIndex = 0;
            scrollOffset = 0; // Reset scroll offset
        } else {
            // Otherwise, use the scroll offset
            startIndex = scrollOffset;
        }
    }

    /**
     * Updates the maximum scroll offset based on the number of available images.
     */
    private void updateMaxScrollOffset() {
        if (availableImages.size() <= VISIBLE_IMAGES) {
            maxScrollOffset = 0;
        } else {
            maxScrollOffset = availableImages.size() - VISIBLE_IMAGES;
        }

        // Ensure scroll offset is within bounds
        if (scrollOffset > maxScrollOffset) {
            scrollOffset = maxScrollOffset;
        }
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        this.renderBackground(context);

        // If images are still loading, show a loading message
        if (!imagesLoaded) {
            context.drawCenteredTextWithShadow(this.textRenderer,
                    Text.literal("Loading images...").setStyle(Style.EMPTY.withBold(true)),
                    this.width / 2, this.height / 2, 0xFFFFFF);
            super.render(context, mouseX, mouseY, delta);
            return;
        }

        // Draw panel background
        context.fill(panelX, panelY, panelX + panelWidth, panelY + panelHeight, BACKGROUND_COLOR);

        // Draw panel border
        context.fill(panelX, panelY, panelX + panelWidth, panelY + 2, BORDER_COLOR); // Top
        context.fill(panelX, panelY + panelHeight - 2, panelX + panelWidth, panelY + panelHeight, BORDER_COLOR); // Bottom
        context.fill(panelX, panelY, panelX + 2, panelY + panelHeight, BORDER_COLOR); // Left
        context.fill(panelX + panelWidth - 2, panelY, panelX + panelWidth, panelY + panelHeight, BORDER_COLOR); // Right

        // Draw header
        context.fill(panelX, panelY, panelX + panelWidth, panelY + 30, HEADER_COLOR);

        // Draw title
        context.drawCenteredTextWithShadow(this.textRenderer,
                Text.literal("Town Image Selection").setStyle(Style.EMPTY.withBold(true)),
                this.width / 2, panelY + 10, 0xFFFFFF);

        // Draw instructions
        context.drawCenteredTextWithShadow(this.textRenderer,
                Text.literal("Click an image to select and edit it • Scroll to see more").setStyle(Style.EMPTY.withColor(Formatting.GRAY)),
                this.width / 2, panelY + 40, 0xAAAAAA);

        // Calculate how many images to display
        int imagesToDisplay = Math.min(VISIBLE_IMAGES, availableImages.size());

        // Draw scroll indicators if needed
        if (scrollOffset > 0) {
            // Left arrow
            String leftArrow = "◀";
            context.drawTextWithShadow(this.textRenderer, leftArrow, startX - 20, centerY - 5, 0xFFFFFF);
        }

        if (scrollOffset < maxScrollOffset) {
            // Right arrow
            String rightArrow = "▶";
            context.drawTextWithShadow(this.textRenderer, rightArrow, startX + totalWidth + 10, centerY - 5, 0xFFFFFF);
        }

        // Draw images in a horizontal row
        for (int i = 0; i < imagesToDisplay && i + startIndex < availableImages.size(); i++) {
            int x = startX + i * (IMAGE_SIZE + IMAGE_PADDING);
            int y = centerY - (IMAGE_SIZE / 2);

            // Get image identifier
            String imageName = availableImages.get(i + startIndex);
            Identifier imageId = TownImageUtil.getImageIdentifier(town, imageName);

            // Draw image background (circular)
            boolean isSelected = (i + startIndex) == selectedImageIndex;
            int bgColor = isSelected ? SELECTED_COLOR : UNSELECTED_COLOR;

            // Calculate center and radius of the circular image
            int imageCenterX = x + IMAGE_SIZE / 2;
            int imageCenterY = y + IMAGE_SIZE / 2;
            int imageRadius = IMAGE_SIZE / 2;

            // Draw circular background (using a more efficient method)
            drawEfficientCircle(context, imageCenterX, imageCenterY, imageRadius + 2, bgColor);

            // Draw image if available
            if (imageId != null) {
                try {
                    // Get image settings if available
                    TownImageUtil.ImageSettings settings = TownImageUtil.getImageSettings(town, imageName);

                    // Draw the image with circular masking
                    if (settings != null) {
                        // Apply image transformations
                        int scaledSize = (int)(IMAGE_SIZE * settings.scale);
                        int imageX = x + (IMAGE_SIZE - scaledSize) / 2 + settings.offsetX;
                        int imageY = y + (IMAGE_SIZE - scaledSize) / 2 + settings.offsetY;

                        // Draw the image with efficient circular masking
                        drawEfficientCircularImage(context, imageId, imageX, imageY, scaledSize, scaledSize,
                                          imageCenterX, imageCenterY, imageRadius);
                    } else {
                        // Draw without transformations but centered
                        drawEfficientCircularImage(context, imageId, x, y, IMAGE_SIZE, IMAGE_SIZE,
                                          imageCenterX, imageCenterY, imageRadius);
                    }

                    // Draw circle border on top to ensure clean edges
                    drawEfficientCircleOutline(context, imageCenterX, imageCenterY, imageRadius, bgColor);
                } catch (Exception e) {
                    // If there's an error drawing the texture, show a placeholder
                    drawPlaceholder(context, x, y, imageName);
                }
            } else {
                // Draw a placeholder if no image is available
                drawPlaceholder(context, x, y, imageName);
            }

            // Draw image name
            String displayName = imageName.length() > 12 ? imageName.substring(0, 10) + "..." : imageName;
            context.drawCenteredTextWithShadow(this.textRenderer, displayName, x + IMAGE_SIZE / 2, y + IMAGE_SIZE + 15, 0xFFFFFF);
        }

        // Draw 'ESC to exit' at the bottom
        context.drawCenteredTextWithShadow(this.textRenderer,
                Text.literal("Press ESC to exit").setStyle(Style.EMPTY.withColor(Formatting.GRAY)),
                this.width / 2, panelY + panelHeight - 20, 0xAAAAAA);

        // Draw scroll position indicator
        if (availableImages.size() > VISIBLE_IMAGES) {
            int indicatorWidth = panelWidth - 40;
            int indicatorX = panelX + 20;
            int indicatorY = panelY + panelHeight - 40;

            // Draw background track
            context.fill(indicatorX, indicatorY, indicatorX + indicatorWidth, indicatorY + 4, 0x55FFFFFF);

            // Draw position indicator
            float progress = (float) scrollOffset / maxScrollOffset;
            int handleWidth = Math.max(40, indicatorWidth / maxScrollOffset);
            int handleX = indicatorX + (int)((indicatorWidth - handleWidth) * progress);
            context.fill(handleX, indicatorY, handleX + handleWidth, indicatorY + 4, 0xFFFFFFFF);
        }

        super.render(context, mouseX, mouseY, delta);
    }

    /**
     * Draws a filled circle using a more efficient algorithm.
     * Uses horizontal lines to fill the circle instead of checking each pixel.
     */
    private void drawEfficientCircle(DrawContext context, int centerX, int centerY, int radius, int color) {
        // Draw a filled circle using horizontal lines
        for (int y = -radius; y <= radius; y++) {
            int x = (int) Math.sqrt(radius * radius - y * y);
            context.fill(centerX - x, centerY + y, centerX + x + 1, centerY + y + 1, color);
        }
    }



    /**
     * Draws a circle outline using the midpoint circle algorithm.
     * Much more efficient than the previous implementation.
     */
    private void drawEfficientCircleOutline(DrawContext context, int centerX, int centerY, int radius, int color) {
        // Use the midpoint circle algorithm
        int x = radius;
        int y = 0;
        int err = 0;

        while (x >= y) {
            // Draw 8 points for each step to complete the circle
            context.fill(centerX + x, centerY + y, centerX + x + 1, centerY + y + 1, color);
            context.fill(centerX + y, centerY + x, centerX + y + 1, centerY + x + 1, color);
            context.fill(centerX - y, centerY + x, centerX - y + 1, centerY + x + 1, color);
            context.fill(centerX - x, centerY + y, centerX - x + 1, centerY + y + 1, color);
            context.fill(centerX - x, centerY - y, centerX - x + 1, centerY - y + 1, color);
            context.fill(centerX - y, centerY - x, centerX - y + 1, centerY - x + 1, color);
            context.fill(centerX + y, centerY - x, centerX + y + 1, centerY - x + 1, color);
            context.fill(centerX + x, centerY - y, centerX + x + 1, centerY - y + 1, color);

            y += 1;
            if (err <= 0) {
                err += 2 * y + 1;
            }
            if (err > 0) {
                x -= 1;
                err -= 2 * x + 1;
            }
        }
    }

    /**
     * Draws an image with proper circular clipping.
     * Uses pixel-by-pixel rendering to ensure the image is only visible within the circle.
     */
    private void drawEfficientCircularImage(DrawContext context, Identifier imageId, int x, int y, int width, int height,
                                  int circleCenterX, int circleCenterY, int circleRadius) {
        // Calculate the bounding box of the circle
        int left = circleCenterX - circleRadius;
        int top = circleCenterY - circleRadius;
        int right = circleCenterX + circleRadius;
        int bottom = circleCenterY + circleRadius;

        // Constrain image position to ensure it stays within the circle bounds
        int maxOffset = (int)(circleRadius * 0.7); // Allow some movement but not too much

        // Calculate the center of the image
        int imageCenterX = x + width / 2;
        int imageCenterY = y + height / 2;

        // Calculate the offset from the circle center
        int offsetX = imageCenterX - circleCenterX;
        int offsetY = imageCenterY - circleCenterY;

        // Calculate the distance from the circle center
        double distance = Math.sqrt(offsetX * offsetX + offsetY * offsetY);

        // If the image center is too far from the circle center, move it closer
        if (distance > maxOffset) {
            double ratio = maxOffset / distance;
            offsetX = (int)(offsetX * ratio);
            offsetY = (int)(offsetY * ratio);

            // Recalculate image position
            imageCenterX = circleCenterX + offsetX;
            imageCenterY = circleCenterY + offsetY;
            x = imageCenterX - width / 2;
            y = imageCenterY - height / 2;
        }

        // Pre-calculate radius squared for efficiency
        int radiusSquared = circleRadius * circleRadius;

        // Draw the image pixel by pixel, only within the circle
        for (int py = top; py < bottom; py++) {
            for (int px = left; px < right; px++) {
                int dx = px - circleCenterX;
                int dy = py - circleCenterY;
                if (dx * dx + dy * dy <= radiusSquared) {
                    // This point is inside the circle, calculate the corresponding image pixel
                    int imgX = px - x;
                    int imgY = py - y;

                    // Only draw if the pixel is within the image bounds
                    if (imgX >= 0 && imgX < width && imgY >= 0 && imgY < height) {
                        // Draw a single pixel from the image
                        context.drawTexture(imageId,
                                          px, py, // destination position
                                          imgX, imgY, // source position in texture
                                          1, 1, // size to draw (1x1 pixel)
                                          width, height); // texture dimensions
                    }
                }
            }
        }
    }

    /**
     * Draws a placeholder for missing images.
     * Optimized to use fewer fill operations.
     */
    private void drawPlaceholder(DrawContext context, int x, int y, String imageName) {
        // Fill with a gray background
        context.fill(x, y, x + IMAGE_SIZE, y + IMAGE_SIZE, 0xFF666666);

        // Draw a simplified pattern to indicate missing texture
        // Use larger blocks to reduce the number of fill operations
        for (int i = 0; i < IMAGE_SIZE; i += 32) {
            for (int j = 0; j < IMAGE_SIZE; j += 32) {
                boolean isAlternate = ((i / 32) + (j / 32)) % 2 == 0;
                context.fill(x + i, y + j, x + i + 16, y + j + 16, isAlternate ? 0xFFFF00FF : 0xFF000000);
                context.fill(x + i + 16, y + j, x + i + 32, y + j + 16, isAlternate ? 0xFF000000 : 0xFFFF00FF);
                context.fill(x + i, y + j + 16, x + i + 16, y + j + 32, isAlternate ? 0xFF000000 : 0xFFFF00FF);
                context.fill(x + i + 16, y + j + 16, x + i + 32, y + j + 32, isAlternate ? 0xFFFF00FF : 0xFF000000);
            }
        }

        // Draw a question mark in the center
        String noImg = "?";
        context.drawCenteredTextWithShadow(this.textRenderer, noImg, x + IMAGE_SIZE / 2, y + IMAGE_SIZE / 2 - 4, 0xFFFFFF);

        // Draw the image name at the bottom
        String shortName = imageName.length() > 10 ? imageName.substring(0, 7) + "..." : imageName;
        context.drawCenteredTextWithShadow(this.textRenderer,
                Text.literal("Missing: " + shortName).setStyle(Style.EMPTY.withColor(Formatting.RED)),
                x + IMAGE_SIZE / 2, y + IMAGE_SIZE - 15, 0xFFFFFF);
    }

    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        // If images are still loading, ignore clicks
        if (!imagesLoaded) {
            return super.mouseClicked(mouseX, mouseY, button);
        }

        // Check if clicked on an image
        if (button == 0) { // Left click
            // Calculate how many images to display
            int imagesToDisplay = Math.min(VISIBLE_IMAGES, availableImages.size());

            // Check if clicked on left arrow
            if (scrollOffset > 0 && mouseX >= startX - 25 && mouseX <= startX - 10 &&
                mouseY >= centerY - 10 && mouseY <= centerY + 10) {
                scrollOffset--;
                calculateRenderingValues(); // Update rendering values
                playClickSound();
                return true;
            }

            // Check if clicked on right arrow
            if (scrollOffset < maxScrollOffset && mouseX >= startX + totalWidth + 10 && mouseX <= startX + totalWidth + 25 &&
                mouseY >= centerY - 10 && mouseY <= centerY + 10) {
                scrollOffset++;
                calculateRenderingValues(); // Update rendering values
                playClickSound();
                return true;
            }

            // Check if clicked on an image
            for (int i = 0; i < imagesToDisplay && i + startIndex < availableImages.size(); i++) {
                int x = startX + i * (IMAGE_SIZE + IMAGE_PADDING);
                int y = centerY - (IMAGE_SIZE / 2);

                // Check if click is inside the circular image area
                int imageCenterX = x + IMAGE_SIZE / 2;
                int imageCenterY = y + IMAGE_SIZE / 2;
                int radius = IMAGE_SIZE / 2;

                if (isPointInCircle((int)mouseX, (int)mouseY, imageCenterX, imageCenterY, radius)) {
                    int clickedIndex = i + startIndex;

                    // Check if this image is already selected
                    if (clickedIndex == selectedImageIndex) {
                        // Already selected, open the editor
                        String imageName = availableImages.get(selectedImageIndex);
                        playClickSound();

                        // Check if town is valid before opening the editor
                        if (town == null) {
                            com.pokecobble.Pokecobbleclaim.LOGGER.error("Cannot open image editor: town is null");
                            // Show an error message to the user
                            if (this.client.player != null) {
                                this.client.player.sendMessage(Text.literal("Error: Could not open image editor. Town data is missing.").setStyle(Style.EMPTY.withColor(Formatting.RED)), false);
                            }
                            return true;
                        }

                        // Open the image editor screen
                        this.client.setScreen(new ImageEditorScreen(this, town, imageName));
                    } else {
                        // Not selected yet, just select it
                        selectedImageIndex = clickedIndex;

                        // Get the selected image name
                        String imageName = availableImages.get(selectedImageIndex);

                        // Save the selected image
                        saveTownImage(imageName);

                        playClickSound();
                    }
                    return true;
                }
            }

            // Check if clicked on scroll bar
            if (availableImages.size() > VISIBLE_IMAGES) {
                int indicatorWidth = panelWidth - 40;
                int indicatorX = panelX + 20;
                int indicatorY = panelY + panelHeight - 40;

                if (mouseY >= indicatorY && mouseY <= indicatorY + 4 &&
                    mouseX >= indicatorX && mouseX <= indicatorX + indicatorWidth) {
                    // Calculate new scroll position based on click position
                    float clickPosition = (float)(mouseX - indicatorX) / indicatorWidth;
                    scrollOffset = Math.round(clickPosition * maxScrollOffset);
                    scrollOffset = Math.max(0, Math.min(scrollOffset, maxScrollOffset));
                    calculateRenderingValues(); // Update rendering values
                    playClickSound();
                    return true;
                }
            }
        }

        return super.mouseClicked(mouseX, mouseY, button);
    }

    /**
     * Plays the button click sound.
     */
    private void playClickSound() {
        SoundUtil.playButtonClickSound();
    }

    /**
     * Checks if a point is inside a circle.
     */
    private boolean isPointInCircle(int pointX, int pointY, int circleX, int circleY, int radius) {
        int dx = pointX - circleX;
        int dy = pointY - circleY;
        return dx * dx + dy * dy <= radius * radius;
    }

    @Override
    public boolean mouseScrolled(double mouseX, double mouseY, double amount) {
        // If images are still loading, ignore scrolling
        if (!imagesLoaded) {
            return super.mouseScrolled(mouseX, mouseY, amount);
        }

        if (availableImages.size() > VISIBLE_IMAGES) {
            if (amount > 0) { // Scroll up/left
                scrollOffset = Math.max(0, scrollOffset - 1);
                calculateRenderingValues(); // Update rendering values
                return true;
            } else if (amount < 0) { // Scroll down/right
                scrollOffset = Math.min(maxScrollOffset, scrollOffset + 1);
                calculateRenderingValues(); // Update rendering values
                return true;
            }
        }
        return super.mouseScrolled(mouseX, mouseY, amount);
    }

    @Override
    public void close() {
        // If an image is selected, make sure it's applied immediately
        if (selectedImageIndex >= 0 && selectedImageIndex < availableImages.size() && town != null) {
            String imageName = availableImages.get(selectedImageIndex);

            // Save the selected image to the town
            town.setImage(imageName);

            // Apply any existing settings immediately on the client side
            TownImageUtil.ImageSettings settings = TownImageUtil.getImageSettings(town, imageName);
            if (settings != null) {
                TownImageUtil.applyImageSettingsLocally(town, imageName, settings.scale, settings.offsetX, settings.offsetY);

                // Save the settings through the town settings system (this will sync to all players)
                TownImageUtil.saveImageSettings(town, imageName, settings.scale, settings.offsetX, settings.offsetY);
            } else {
                // If no existing settings, save the image change through proper client-side synchronization
                try {
                    // Use the proper client-side synchronization method
                    com.pokecobble.town.config.TownSettingsManager.updateCurrentPlayerTownSetting("image", imageName);

                    // Add default image settings
                    java.util.Map<String, Object> imageSettingsMap = new java.util.HashMap<>();
                    imageSettingsMap.put("scale", 1.0f);
                    imageSettingsMap.put("offsetX", 0);
                    imageSettingsMap.put("offsetY", 0);
                    com.pokecobble.town.config.TownSettingsManager.updateCurrentPlayerTownSetting("imageSettings", imageSettingsMap);
                } catch (Exception e) {
                    Pokecobbleclaim.LOGGER.error("Failed to save image change via town settings: " + e.getMessage());
                    // Fallback to old synchronization method
                    TownImageSynchronizer.requestTownImageUpdate(town.getId());
                }
            }

            // Mark the town as changed to trigger UI updates
            town.markChanged(Town.ASPECT_IMAGE);
        }

        this.client.setScreen(parent);
    }

    /**
     * Loads available images for the town.
     * Optimized to handle errors gracefully and avoid blocking the main thread.
     */
    private void loadAvailableImages() {
        availableImages.clear();

        try {
            // Add test images
            // These are hardcoded since we can't easily list resources at runtime
            availableImages.add("test_test1"); // test1.jpg
            availableImages.add("test_test2"); // test2.png
            availableImages.add("test_test3"); // test3.jpg

            // Then, if town exists, load town-specific images
            if (town != null) {
                // Check if town directory exists
                Path townDir = Paths.get(TOWN_IMAGES_PATH + town.getName());
                if (Files.exists(townDir)) {
                    // List all image files in the directory
                    List<Path> imagePaths = Files.list(townDir)
                        .filter(path -> {
                            String fileName = path.getFileName().toString().toLowerCase();
                            return fileName.endsWith(".png") || fileName.endsWith(".jpg") || fileName.endsWith(".jpeg");
                        })
                        .toList(); // Collect to a list to avoid stream issues

                    // Process the collected paths
                    for (Path path : imagePaths) {
                        String fileName = path.getFileName().toString();
                        // Remove extension
                        fileName = fileName.substring(0, fileName.lastIndexOf('.'));
                        availableImages.add(fileName);
                    }
                }

                // Check if town has a selected image
                String currentImage = town.getImage();
                if (currentImage != null && !currentImage.isEmpty()) {
                    for (int i = 0; i < availableImages.size(); i++) {
                        if (availableImages.get(i).equals(currentImage)) {
                            selectedImageIndex = i;
                            break;
                        }
                    }
                }
            }
        } catch (Exception e) {
            com.pokecobble.Pokecobbleclaim.LOGGER.error("Error loading town images: " + e.getMessage());
            // Add a fallback image if loading fails
            if (availableImages.isEmpty()) {
                availableImages.add("test_test1"); // Ensure at least one image is available
            }
        }
    }

    /**
     * Saves the selected image for the town.
     */
    private void saveTownImage(String imageName) {
        if (town != null) {
            try {
                // Set the image name in the town object locally
                town.setImage(imageName);

                // Use the proper client-side synchronization method
                com.pokecobble.town.config.TownSettingsManager.updateCurrentPlayerTownSetting("image", imageName);

                // Also save default image settings
                java.util.Map<String, Object> imageSettingsMap = new java.util.HashMap<>();
                imageSettingsMap.put("scale", 1.0f);
                imageSettingsMap.put("offsetX", 0);
                imageSettingsMap.put("offsetY", 0);
                com.pokecobble.town.config.TownSettingsManager.updateCurrentPlayerTownSetting("imageSettings", imageSettingsMap);

                // Log the change
                com.pokecobble.Pokecobbleclaim.LOGGER.info("Saved town image: " + imageName + " for town: " + town.getName() + " (syncing to server)");
            } catch (Exception e) {
                // Log the error
                com.pokecobble.Pokecobbleclaim.LOGGER.error("Error saving town image: " + e.getMessage(), e);
                // Show an error message to the user
                if (this.client != null && this.client.player != null) {
                    this.client.player.sendMessage(Text.literal("Error: Could not save town image. " + e.getMessage()).setStyle(Style.EMPTY.withColor(Formatting.RED)), false);
                }
            }
        } else {
            com.pokecobble.Pokecobbleclaim.LOGGER.error("Cannot save town image: town is null");
            // Show an error message to the user
            if (this.client != null && this.client.player != null) {
                this.client.player.sendMessage(Text.literal("Error: Could not save town image. Town data is missing.").setStyle(Style.EMPTY.withColor(Formatting.RED)), false);
            }
        }
    }


}
